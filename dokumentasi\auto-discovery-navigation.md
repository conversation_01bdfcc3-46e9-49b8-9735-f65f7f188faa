# Auto-Discovery Navigation - RajaMenu

## 📋 **Overview**

Auto-Discovery Navigation adalah implementasi yang membaca langsung dari resource properties FilamentPHP seperti `$navigationGroup` dan `$navigationParentItem` tanpa perlu mendefinisikan menu manual di panel provider.

## 🎯 **Fitur Utama**

### ✅ **Automatic Resource Detection**
- Membaca semua resources dan pages dari panel
- Extract navigation properties secara otomatis
- Support hierarchical navigation dengan parent-child

### ✅ **Smart Parent Item Creation**
- Otomatis membuat parent items yang tidak ada
- Guess icon dan group berdasarkan children
- Maintain navigation structure

### ✅ **Resource Properties Support**
```php
protected static ?string $navigationGroup = 'System';
protected static ?string $navigationParentItem = 'Data Management';
protected static ?string $navigationLabel = 'Kategori';
protected static ?string $navigationIcon = 'heroicon-o-list-bullet';
protected static ?int $navigationSort = 1;
protected static bool $shouldRegisterNavigation = true;
```

## 🔧 **Implementation**

### **1. FlyoutNavigationService Enhancement**

#### **Auto-Discovery Method**
```php
public static function discoverNavigationFromPanel(string $panelId = 'admin'): array
{
    $panel = \Filament\Facades\Filament::getPanel($panelId);
    $navigationItems = [];
    $childItems = [];

    // Process resources
    foreach ($panel->getResources() as $resource) {
        $item = self::extractNavigationFromResource($resource);
        if ($item) {
            if ($item['parent_item']) {
                $childItems[$item['parent_item']][] = $item;
            } else {
                $navigationItems[$item['group']][] = $item;
            }
        }
    }

    // Add missing parent items
    self::addMissingParentItems($navigationItems, $childItems);

    return [
        'navigationItems' => $navigationItems,
        'childItems' => $childItems,
    ];
}
```

#### **Resource Extraction**
```php
protected static function extractNavigationFromResource(string $resource): ?array
{
    // Check if should register navigation
    if (method_exists($resource, 'shouldRegisterNavigation') && 
        !$resource::shouldRegisterNavigation()) {
        return null;
    }

    // Extract properties
    $label = $resource::getNavigationLabel();
    $icon = $resource::getNavigationIcon();
    $group = $resource::getNavigationGroup();
    $parentItem = $resource::getNavigationParentItem();
    $sort = $resource::getNavigationSort();
    $badge = $resource::getNavigationBadge();

    return [
        'label' => $label,
        'icon' => $icon,
        'url' => $resource::getUrl('index'),
        'group' => $group,
        'parent_item' => $parentItem,
        'sort' => $sort ?? 0,
        'badge' => $badge,
        'type' => 'resource',
        'class' => $resource,
    ];
}
```

### **2. Auto-Flyout View**

#### **View Implementation**
```blade
{{-- Auto-Discovery Flyout Navigation --}}
@php
    use Modules\RajaMenu\Services\FlyoutNavigationService;
    
    $discoveredNav = FlyoutNavigationService::discoverNavigationFromPanel('admin');
    $navigationItems = $discoveredNav['navigationItems'];
    $childItems = $discoveredNav['childItems'];
@endphp

<nav class="auto-flyout-nav">
    @foreach($navigationItems as $groupName => $items)
        <div class="nav-group">
            <div class="nav-group-label">{{ $groupName }}</div>
            <div class="nav-dropdown">
                @foreach($items as $item)
                    @if(isset($childItems[$item['label']]))
                        {{-- Parent with children --}}
                        <div class="nav-item has-children">
                            {{ $item['label'] }}
                            <div class="nav-sub-dropdown">
                                @foreach($childItems[$item['label']] as $child)
                                    <a href="{{ $child['url'] }}">{{ $child['label'] }}</a>
                                @endforeach
                            </div>
                        </div>
                    @else
                        {{-- Regular item --}}
                        <a href="{{ $item['url'] }}">{{ $item['label'] }}</a>
                    @endif
                @endforeach
            </div>
        </div>
    @endforeach
</nav>
```

### **3. AdminPanelProvider Update**

#### **Simplified Configuration**
```php
class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->navigationGroups([
                NavigationGroup::make('Cms'),
                NavigationGroup::make('System'),
                NavigationGroup::make('Pengaturan'),
            ])
            ->renderHook(
                'panels::topbar.start',
                fn() => view('rajamenu::navigation.auto-flyout')
            )
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages');
    }
}
```

## 📝 **Resource Configuration Examples**

### **Example 1: Child Resource**
```php
class UserResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Karyawan';
    protected static ?string $navigationGroup = 'Pengaturan';
    protected static ?string $navigationParentItem = 'Business'; // Parent item
    protected static ?int $navigationSort = 3;
    protected static bool $shouldRegisterNavigation = true; // Enable navigation
}
```

### **Example 2: Grouped Resource**
```php
class KategoriResource extends Resource
{
    protected static ?string $model = Kategori::class;
    protected static ?string $navigationGroup = 'System';
    protected static ?string $navigationParentItem = 'Data Management'; // Parent item
    protected static ?string $navigationLabel = 'Kategori';
    protected static ?string $navigationIcon = 'heroicon-o-list-bullet';
    protected static bool $shouldRegisterNavigation = true; // Enable navigation
}
```

### **Example 3: Top-Level Resource**
```php
class DashboardResource extends Resource
{
    protected static ?string $navigationGroup = 'Main';
    protected static ?string $navigationLabel = 'Dashboard';
    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static ?int $navigationSort = 1;
    protected static bool $shouldRegisterNavigation = true;
    // No navigationParentItem = top-level item
}
```

## 🎨 **Smart Parent Item Creation**

### **Automatic Parent Detection**
```php
protected static function addMissingParentItems(array &$navigationItems, array $childItems): void
{
    $parentItemsNeeded = array_keys($childItems);
    
    foreach ($parentItemsNeeded as $parentLabel) {
        $found = false;
        
        // Check if parent exists
        foreach ($navigationItems as $group => $items) {
            foreach ($items as $item) {
                if ($item['label'] === $parentLabel) {
                    $found = true;
                    break 2;
                }
            }
        }
        
        // Create missing parent
        if (!$found) {
            $parentGroup = self::guessParentGroup($parentLabel, $childItems);
            $parentIcon = self::guessParentIcon($parentLabel);
            
            $navigationItems[$parentGroup][] = [
                'label' => $parentLabel,
                'icon' => $parentIcon,
                'url' => '#',
                'group' => $parentGroup,
                'type' => 'parent',
            ];
        }
    }
}
```

### **Icon Mapping**
```php
protected static function guessParentIcon(string $parentLabel): string
{
    $iconMap = [
        'Business' => 'heroicon-o-building-office',
        'Data Management' => 'heroicon-o-database',
        'System Tools' => 'heroicon-o-wrench-screwdriver',
        'Content' => 'heroicon-o-book-open',
        'Settings' => 'heroicon-o-cog-6-tooth',
    ];

    return $iconMap[$parentLabel] ?? 'heroicon-o-folder';
}
```

## 🌐 **Current Implementation Status**

### **✅ Active Resources**
1. **UserResource**
   - Group: `Pengaturan`
   - Parent: `Business`
   - Label: `Karyawan`

2. **KategoriResource**
   - Group: `System`
   - Parent: `Data Management`
   - Label: `Kategori`

3. **TokoResource**
   - Group: `Pengaturan`
   - Parent: `Business`
   - Label: `Usaha`

4. **MetodePembayaranUtamaResource**
   - Group: `System`
   - Parent: `Data Management`
   - Label: `Metode Pembayaran`

### **✅ Auto-Generated Parents**
- **Business** (in Pengaturan group)
- **Data Management** (in System group)

## 🎯 **Benefits**

### **✅ Developer Experience**
- **No Manual Menu Definition** - Resources define their own navigation
- **Automatic Hierarchy** - Parent-child relationships work automatically
- **Consistent Structure** - Navigation follows resource organization
- **Easy Maintenance** - Add/remove resources automatically updates navigation

### **✅ Flexibility**
- **Per-Resource Control** - Each resource controls its navigation
- **Dynamic Grouping** - Groups determined by resource properties
- **Smart Defaults** - Missing parents created automatically
- **Badge Support** - Resource badges automatically included

### **✅ Performance**
- **Runtime Discovery** - Navigation built from actual panel resources
- **Efficient Extraction** - Only processes registered resources
- **Fallback Support** - Falls back to static navigation if needed

## 🔄 **Migration from Manual Navigation**

### **Before (Manual)**
```php
->navigationItems([
    NavigationItem::make('Business')
        ->icon('heroicon-o-building-office')
        ->url('#')
        ->group('Pengaturan'),
    
    NavigationItem::make('Usaha')
        ->icon('heroicon-o-building-storefront')
        ->url(fn(): string => TokoResource::getUrl('index'))
        ->group('Pengaturan')
        ->parentItem('Business'),
])
```

### **After (Auto-Discovery)**
```php
// In TokoResource.php
protected static ?string $navigationGroup = 'Pengaturan';
protected static ?string $navigationParentItem = 'Business';
protected static ?string $navigationLabel = 'Usaha';
protected static bool $shouldRegisterNavigation = true;

// In AdminPanelProvider.php
->renderHook(
    'panels::topbar.start',
    fn() => view('rajamenu::navigation.auto-flyout')
)
```

## 🎉 **Conclusion**

Auto-Discovery Navigation menyediakan:
- ✅ **Zero Configuration** - Navigation otomatis dari resource properties
- ✅ **Hierarchical Support** - Parent-child relationships
- ✅ **Smart Defaults** - Automatic parent creation
- ✅ **Developer Friendly** - Resource-centric navigation definition
- ✅ **Maintainable** - Self-organizing navigation structure

**Navigation sekarang mengikuti struktur resource secara otomatis!** 🚀
