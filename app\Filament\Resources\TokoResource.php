<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TokoResource\Pages;
use App\Filament\Resources\TokoResource\RelationManagers;
use App\Models\Toko;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TokoResource extends Resource
{
    protected static ?string $model = Toko::class;
    protected static bool $shouldRegisterNavigation = true; // Aktifkan navigation
    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';
    protected static ?string $navigationGroup = 'Pengaturan';
    protected static ?string $navigationParentItem = 'Business'; // Parent item
    protected static ?string $navigationLabel = 'Usaha';
    protected static ?string $slug = 'pengaturan/toko';
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Forms\Components\TextInput::make('kode')->required(),
// Forms\Components\TextInput::make('lokasi')->required(),
// Forms\Components\TextInput::make('sub')->required(),
// Forms\Components\TextInput::make('jenis')->required(),

Section::make('informasi usaha')
->schema([
    Forms\Components\TextInput::make('nama')->required(),
   Textarea::make('alamat')->required(),
Forms\Components\TextInput::make('telpon')->required(),
Forms\Components\TextInput::make('email')->required(),
])->columnSpan(1),
Grid::make()
->schema([
 
    FileUpload::make('foto')->directory('toko') ,
    FileUpload::make('logo')->directory('toko')->imageEditor() ,
    FileUpload::make('favicon')->directory('toko') ,
])->columnSpan(2),



 
 
 
 
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
 
// Tables\Columns\TextColumn::make('lokasi')->sortable()->searchable(),
// Tables\Columns\TextColumn::make('sub')->sortable()->searchable(),
// Tables\Columns\TextColumn::make('jenis')->sortable()->searchable(),
Tables\Columns\TextColumn::make('nama')->sortable()->searchable(),
// Tables\Columns\TextColumn::make('alamat')->sortable()->searchable(),
// Tables\Columns\TextColumn::make('telpon')->sortable()->searchable(),
Tables\Columns\TextColumn::make('email')->sortable()->searchable(),
 
ImageColumn::make('foto'),
ImageColumn::make('logo'),
 
 
 
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTokos::route('/'),
            'create' => Pages\CreateToko::route('/create'),
            // 'view' => Pages\ViewToko::route('/{record}'),
            'edit' => Pages\EditToko::route('/{record}/edit'),
        ];
    }
}
