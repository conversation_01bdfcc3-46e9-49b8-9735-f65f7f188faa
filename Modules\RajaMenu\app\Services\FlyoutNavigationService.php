<?php

namespace Modules\RajaMenu\Services;

use Filament\Navigation\NavigationItem;
use Illuminate\Support\Collection;

class FlyoutNavigationService
{
    /**
     * Organize navigation items into parent-child structure for flyout menu
     */
    public static function organizeNavigationItems(array $navigationItems): array
    {
        $organized = [
            'groups' => [],
            'childItems' => []
        ];

        $groupedItems = collect($navigationItems)->groupBy(function ($item) {
            return $item->getGroup() ?? 'Default';
        });

        foreach ($groupedItems as $groupName => $items) {
            $parentItems = [];
            $childItems = [];

            foreach ($items as $item) {
                $parentItem = $item->getParentItem();
                
                if ($parentItem) {
                    // This is a child item
                    if (!isset($childItems[$parentItem])) {
                        $childItems[$parentItem] = [];
                    }
                    $childItems[$parentItem][] = $item;
                } else {
                    // This is a parent item
                    $parentItems[] = $item;
                }
            }

            $organized['groups'][$groupName] = $parentItems;
            $organized['childItems'] = array_merge($organized['childItems'], $childItems);
        }

        return $organized;
    }

    /**
     * Create navigation items for CMS group with flyout structure
     */
    public static function createCmsNavigationItems(): array
    {
        return [
            // Parent: Content Management
            NavigationItem::make('Content')
                ->icon('heroicon-o-book-open')
                ->url('#')
                ->group('Cms')
                ->sort(1),

            // Child items untuk Content
            NavigationItem::make('Konten')
                ->icon('heroicon-o-document-text')
                ->url(fn(): string => \App\Filament\Resources\CmsResource::getUrl('index'))
                ->group('Cms')
                ->parentItem('Content')
                ->sort(1),

            NavigationItem::make('Artikel')
                ->icon('heroicon-o-newspaper')
                ->url(fn(): string => \App\Filament\Pages\Cms\Kategori::getUrl())
                ->group('Cms')
                ->parentItem('Content')
                ->sort(2),

            NavigationItem::make('Media Library')
                ->icon('heroicon-o-photo')
                ->url('#')
                ->group('Cms')
                ->parentItem('Content')
                ->sort(3),

            NavigationItem::make('Kategori artikel')
                ->icon('heroicon-o-tag')
                ->url(fn(): string => \App\Filament\Pages\Cms\Kategori::getUrl())
                ->group('Cms')
                ->parentItem('Content')
                ->sort(4),

            NavigationItem::make('Menu')
                ->icon('heroicon-o-queue-list')
                ->url(fn(): string => \App\Filament\Resources\MenuWebsiteResource::getUrl('index'))
                ->group('Cms')
                ->parentItem('Content')
                ->sort(5),

            NavigationItem::make('Tema')
                ->icon('heroicon-o-paint-brush')
                ->url(fn(): string => \App\Filament\Resources\CmsResource::getUrl('tema'))
                ->group('Cms')
                ->parentItem('Content')
                ->sort(6),

            // Parent: Design
            NavigationItem::make('Design')
                ->icon('heroicon-o-paint-brush')
                ->url('#')
                ->group('Cms')
                ->sort(10),

            NavigationItem::make('Templates')
                ->icon('heroicon-o-document-duplicate')
                ->url('#')
                ->group('Cms')
                ->parentItem('Design')
                ->sort(1),

            NavigationItem::make('Widgets')
                ->icon('heroicon-o-squares-2x2')
                ->url('#')
                ->group('Cms')
                ->parentItem('Design')
                ->sort(2),
        ];
    }

    /**
     * Create navigation items for System group with flyout structure
     */
    public static function createSystemNavigationItems(): array
    {
        return [
            // Parent: Data Management
            NavigationItem::make('Data Management')
                ->icon('heroicon-o-database')
                ->url('#')
                ->group('System')
                ->sort(1),

            NavigationItem::make('Seluruh kategori')
                ->icon('heroicon-o-list-bullet')
                ->url(fn(): string => \App\Filament\Resources\KategoriResource::getUrl('index'))
                ->group('System')
                ->parentItem('Data Management')
                ->sort(1),

            NavigationItem::make('Metode pembayaran')
                ->icon('heroicon-o-credit-card')
                ->url(fn(): string => \App\Filament\Resources\MetodePembayaranUtamaResource::getUrl('index'))
                ->group('System')
                ->parentItem('Data Management')
                ->sort(2),

            NavigationItem::make('Data')
                ->icon('heroicon-o-table-cells')
                ->url(fn(): string => \App\Filament\Pages\Data::getUrl())
                ->group('System')
                ->parentItem('Data Management')
                ->sort(3),

            // Parent: System Tools
            NavigationItem::make('System Tools')
                ->icon('heroicon-o-wrench-screwdriver')
                ->url('#')
                ->group('System')
                ->sort(10),

            NavigationItem::make('Rute')
                ->icon('heroicon-o-map')
                ->url(fn(): string => \App\Filament\Pages\RouteList::getUrl())
                ->group('System')
                ->parentItem('System Tools')
                ->sort(1),

            NavigationItem::make('Aplikasi')
                ->icon('heroicon-o-squares-plus')
                ->url(fn(): string => \App\Filament\Pages\AplikasiRajaDesain::getUrl())
                ->group('System')
                ->parentItem('System Tools')
                ->sort(2),

            NavigationItem::make('Konfig')
                ->icon('heroicon-o-cog-6-tooth')
                ->url(fn(): string => \App\Filament\Resources\KonfigResource::getUrl('index'))
                ->group('System')
                ->parentItem('System Tools')
                ->sort(3),
        ];
    }

    /**
     * Create navigation items for Settings group with flyout structure
     */
    public static function createSettingsNavigationItems(): array
    {
        return [
            // Parent: Business Management
            NavigationItem::make('Business')
                ->icon('heroicon-o-building-office')
                ->url('#')
                ->group('Pengaturan')
                ->sort(1),

            NavigationItem::make('Usaha')
                ->icon('heroicon-o-building-storefront')
                ->url(fn(): string => \App\Filament\Resources\TokoResource::getUrl('index'))
                ->group('Pengaturan')
                ->parentItem('Business')
                ->sort(1),

            NavigationItem::make('Staff')
                ->icon('heroicon-o-users')
                ->url(fn(): string => \App\Filament\Resources\UserResource::getUrl('index'))
                ->group('Pengaturan')
                ->parentItem('Business')
                ->sort(2),
        ];
    }

    /**
     * Get all organized navigation items
     */
    public static function getAllNavigationItems(): array
    {
        return array_merge(
            self::createCmsNavigationItems(),
            self::createSystemNavigationItems(),
            self::createSettingsNavigationItems()
        );
    }

    /**
     * Get navigation items organized by groups (simple array format)
     */
    public static function getNavigationItems(): array
    {
        return [
            'Cms' => [
                [
                    'label' => 'Content',
                    'icon' => 'heroicon-o-book-open',
                    'url' => '#',
                ]
            ],
            'System' => [
                [
                    'label' => 'Data Management',
                    'icon' => 'heroicon-o-database',
                    'url' => '#',
                ],
                [
                    'label' => 'System Tools',
                    'icon' => 'heroicon-o-wrench-screwdriver',
                    'url' => '#',
                ]
            ],
            'Pengaturan' => [
                [
                    'label' => 'Business',
                    'icon' => 'heroicon-o-building-office',
                    'url' => '#',
                ]
            ]
        ];
    }

    /**
     * Get child navigation items (simple array format)
     */
    public static function getChildNavigationItems(): array
    {
        return [
            'Content' => [
                [
                    'label' => 'Konten',
                    'icon' => 'heroicon-o-document-text',
                    'url' => \App\Filament\Resources\CmsResource::getUrl('index'),
                ],
                [
                    'label' => 'Artikel',
                    'icon' => 'heroicon-o-newspaper',
                    'url' => \App\Filament\Pages\Cms\Kategori::getUrl(),
                ],
                [
                    'label' => 'Media Library',
                    'icon' => 'heroicon-o-photo',
                    'url' => '#',
                ],
                [
                    'label' => 'Menu',
                    'icon' => 'heroicon-o-queue-list',
                    'url' => \App\Filament\Resources\MenuWebsiteResource::getUrl('index'),
                ],
                [
                    'label' => 'Tema',
                    'icon' => 'heroicon-o-paint-brush',
                    'url' => \App\Filament\Resources\CmsResource::getUrl('tema'),
                ]
            ],
            'Data Management' => [
                [
                    'label' => 'Seluruh kategori',
                    'icon' => 'heroicon-o-list-bullet',
                    'url' => \App\Filament\Resources\KategoriResource::getUrl('index'),
                ],
                [
                    'label' => 'Metode pembayaran',
                    'icon' => 'heroicon-o-credit-card',
                    'url' => \App\Filament\Resources\MetodePembayaranUtamaResource::getUrl('index'),
                ],
                [
                    'label' => 'Data',
                    'icon' => 'heroicon-o-table-cells',
                    'url' => \App\Filament\Pages\Data::getUrl(),
                ]
            ],
            'System Tools' => [
                [
                    'label' => 'Rute',
                    'icon' => 'heroicon-o-map',
                    'url' => \App\Filament\Pages\RouteList::getUrl(),
                ],
                [
                    'label' => 'Aplikasi',
                    'icon' => 'heroicon-o-squares-plus',
                    'url' => \App\Filament\Pages\AplikasiRajaDesain::getUrl(),
                ],
                [
                    'label' => 'Konfig',
                    'icon' => 'heroicon-o-cog-6-tooth',
                    'url' => \App\Filament\Resources\KonfigResource::getUrl('index'),
                ]
            ],
            'Business' => [
                [
                    'label' => 'Usaha',
                    'icon' => 'heroicon-o-building-storefront',
                    'url' => \App\Filament\Resources\TokoResource::getUrl('index'),
                ],
                [
                    'label' => 'Staff',
                    'icon' => 'heroicon-o-users',
                    'url' => \App\Filament\Resources\UserResource::getUrl('index'),
                ]
            ]
        ];
    }
}
