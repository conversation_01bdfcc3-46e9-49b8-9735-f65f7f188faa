# RajaMenu Module

Modul untuk mengelola flyout navigation di panel FilamentPHP dengan child menu yang tampil di samping saat mouseover.

## 📁 Struktur Modul

```
Modules/RajaMenu/
├── app/
│   ├── Providers/
│   │   ├── RajaMenuServiceProvider.php
│   │   └── RajaMenuPanelProvider.php
│   ├── Services/
│   │   └── FlyoutNavigationService.php
│   └── Traits/
│       └── HasFlyoutNavigation.php
├── config/
│   └── config.php
├── resources/
│   ├── assets/
│   │   └── css/
│   │       └── flyout-navigation.css
│   └── views/
│       └── navigation/
│           ├── simple-flyout.blade.php
│           └── flyout-navigation.blade.php
└── README.md
```

## 🚀 Instalasi

### 1. Aktifkan Modul
```bash
php artisan module:enable RajaMenu
```

### 2. Publish Assets
```bash
php artisan vendor:publish --tag=rajamenu-flyout-assets
```

### 3. Gunakan Trait di Panel Provider
```php
use Mo<PERSON>les\RajaMenu\Traits\HasFlyoutNavigation;

class AdminPanelProvider extends PanelProvider
{
    use HasFlyoutNavigation;

    public function panel(Panel $panel): Panel
    {
        $panel = $panel
            ->default()
            ->id('admin')
            ->topNavigation(); // Pastikan top navigation aktif

        // Apply flyout navigation
        $panel = $this->applyFlyoutNavigation($panel);

        return $panel;
    }
}
```

## Support

you can join our discord server to get support [TomatoPHP](https://discord.gg/Xqmt35Uh)

## Docs

you can check docs of this package on [Docs](https://docs.tomatophp.com/plugins/tomato-themes)

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## Security

Please see [SECURITY](SECURITY.md) for more information about security.

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
