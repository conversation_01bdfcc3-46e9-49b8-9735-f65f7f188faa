<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Flyout Navigation</title>
    <link href="{{ asset('css/filament-flyout-navigation.css') }}" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Demo Flyout Navigation</h1>
        <p>Hover pada menu untuk melihat flyout effect:</p>
        
        {{-- Demo Navigation --}}
        <nav class="fi-topbar-nav">
            <div class="flex items-center space-x-1">
                {{-- CMS Group --}}
                <div class="fi-topbar-nav-group">
                    <div class="fi-topbar-nav-group-label">
                        Cms
                    </div>
                    <div class="fi-dropdown">
                        {{-- Content Parent --}}
                        <div class="fi-dropdown-item has-children">
                            <div class="flex items-center">
                                <span>📚 Content</span>
                            </div>
                            <div class="fi-dropdown-sub">
                                <a href="#" class="fi-dropdown-item">📄 Konten</a>
                                <a href="#" class="fi-dropdown-item">📰 Artikel</a>
                                <a href="#" class="fi-dropdown-item">📷 Media Library</a>
                                <a href="#" class="fi-dropdown-item">🏷️ Kategori artikel</a>
                                <a href="#" class="fi-dropdown-item">📋 Menu</a>
                                <a href="#" class="fi-dropdown-item">🎨 Tema</a>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- System Group --}}
                <div class="fi-topbar-nav-group">
                    <div class="fi-topbar-nav-group-label">
                        System
                    </div>
                    <div class="fi-dropdown">
                        {{-- Data Management Parent --}}
                        <div class="fi-dropdown-item has-children">
                            <div class="flex items-center">
                                <span>🗄️ Data Management</span>
                            </div>
                            <div class="fi-dropdown-sub">
                                <a href="#" class="fi-dropdown-item">📝 Seluruh kategori</a>
                                <a href="#" class="fi-dropdown-item">💳 Metode pembayaran</a>
                                <a href="#" class="fi-dropdown-item">📊 Data</a>
                            </div>
                        </div>
                        
                        {{-- System Tools Parent --}}
                        <div class="fi-dropdown-item has-children">
                            <div class="flex items-center">
                                <span>🔧 System Tools</span>
                            </div>
                            <div class="fi-dropdown-sub">
                                <a href="#" class="fi-dropdown-item">🗺️ Rute</a>
                                <a href="#" class="fi-dropdown-item">📱 Aplikasi</a>
                                <a href="#" class="fi-dropdown-item">⚙️ Konfig</a>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Pengaturan Group --}}
                <div class="fi-topbar-nav-group">
                    <div class="fi-topbar-nav-group-label">
                        Pengaturan
                    </div>
                    <div class="fi-dropdown">
                        {{-- Business Parent --}}
                        <div class="fi-dropdown-item has-children">
                            <div class="flex items-center">
                                <span>🏢 Business</span>
                            </div>
                            <div class="fi-dropdown-sub">
                                <a href="#" class="fi-dropdown-item">🏪 Usaha</a>
                                <a href="#" class="fi-dropdown-item">👥 Staff</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
            <h3>Cara Kerja:</h3>
            <ol>
                <li>Hover pada label group (Cms, System, Pengaturan)</li>
                <li>Dropdown akan muncul di bawah</li>
                <li>Hover pada item yang memiliki children (Content, Data Management, dll)</li>
                <li>Sub-dropdown akan muncul di samping kanan</li>
                <li>Click pada item untuk navigasi</li>
            </ol>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced hover behavior
            const navGroups = document.querySelectorAll('.fi-topbar-nav-group');
            
            navGroups.forEach(group => {
                const dropdown = group.querySelector('.fi-dropdown');
                let hoverTimeout;
                
                group.addEventListener('mouseenter', function() {
                    clearTimeout(hoverTimeout);
                    dropdown.style.display = 'block';
                    setTimeout(() => {
                        dropdown.classList.add('show');
                    }, 10);
                });
                
                group.addEventListener('mouseleave', function() {
                    dropdown.classList.remove('show');
                    hoverTimeout = setTimeout(() => {
                        dropdown.style.display = 'none';
                    }, 300);
                });
            });
            
            // Handle sub-dropdown behavior
            const dropdownItems = document.querySelectorAll('.fi-dropdown-item.has-children');
            
            dropdownItems.forEach(item => {
                const subDropdown = item.querySelector('.fi-dropdown-sub');
                let subHoverTimeout;
                
                item.addEventListener('mouseenter', function() {
                    clearTimeout(subHoverTimeout);
                    if (subDropdown) {
                        subDropdown.style.display = 'block';
                        setTimeout(() => {
                            subDropdown.classList.add('show');
                        }, 10);
                    }
                });
                
                item.addEventListener('mouseleave', function() {
                    if (subDropdown) {
                        subDropdown.classList.remove('show');
                        subHoverTimeout = setTimeout(() => {
                            subDropdown.style.display = 'none';
                        }, 200);
                    }
                });
            });
        });
    </script>
</body>
</html>
