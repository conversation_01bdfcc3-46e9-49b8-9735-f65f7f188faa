<?php

namespace Modules\RajaMenu\Filament\Resources;

use Modules\RajaMenu\Filament\Resources\RajaMenuItemResource\Pages;
use Modules\RajaMenu\Models\RajaMenuItem;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use Modules\RajaMenu\Services\MenuDiscoveryService;

class RajaMenuItemResource extends Resource
{
    protected static ?string $model = RajaMenuItem::class;
    protected static ?string $navigationIcon = 'heroicon-o-bars-3';
    protected static ?string $navigationLabel = 'Menu Items';
    protected static ?string $navigationGroup = 'RajaMenu';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\Select::make('panel_id')
                            ->label('Panel')
                            ->options(fn () => collect(MenuDiscoveryService::getAvailablePanels())->pluck('name', 'id'))
                            ->required()
                            ->default('admin'),

                        Forms\Components\Select::make('type')
                            ->label('Type')
                            ->options([
                                'resource' => 'Resource',
                                'page' => 'Page',
                                'widget' => 'Widget',
                                'navigation' => 'Navigation Link',
                                'separator' => 'Separator',
                                'group' => 'Group Header',
                            ])
                            ->required()
                            ->live(),

                        Forms\Components\TextInput::make('key')
                            ->label('Key')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->helperText('Unique identifier for this menu item'),

                        Forms\Components\TextInput::make('label')
                            ->label('Label')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('icon')
                            ->label('Icon')
                            ->placeholder('heroicon-o-document')
                            ->maxLength(255)
                            ->helperText('Heroicon name (e.g., heroicon-o-document)'),

                        Forms\Components\TextInput::make('url')
                            ->label('URL')
                            ->url()
                            ->maxLength(255)
                            ->visible(fn (Forms\Get $get) => in_array($get('type'), ['navigation']))
                            ->helperText('Custom URL for navigation items'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Organization')
                    ->schema([
                        Forms\Components\TextInput::make('group')
                            ->label('Group')
                            ->maxLength(255)
                            ->datalist(fn () => RajaMenuItem::distinct()->pluck('group')->filter()->toArray()),

                        Forms\Components\Select::make('parent_id')
                            ->label('Parent Menu')
                            ->relationship('parent', 'label')
                            ->searchable()
                            ->preload(),

                        Forms\Components\TextInput::make('sort_order')
                            ->label('Sort Order')
                            ->numeric()
                            ->default(0)
                            ->helperText('Lower numbers appear first'),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Visibility & Permissions')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),

                        Forms\Components\Toggle::make('is_visible')
                            ->label('Visible')
                            ->default(true),

                        Forms\Components\Toggle::make('is_auto_discovered')
                            ->label('Auto Discovered')
                            ->disabled()
                            ->helperText('Automatically managed by discovery system'),

                        Forms\Components\TagsInput::make('permissions')
                            ->label('Required Permissions')
                            ->placeholder('Add permission...')
                            ->helperText('Permissions required to access this menu item'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Metadata')
                    ->schema([
                        Forms\Components\KeyValue::make('metadata')
                            ->label('Additional Metadata')
                            ->keyLabel('Key')
                            ->valueLabel('Value')
                            ->addActionLabel('Add metadata'),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('panel_id')
                    ->label('Panel')
                    ->badge()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'resource' => 'success',
                        'page' => 'primary',
                        'widget' => 'purple',
                        'navigation' => 'warning',
                        'separator' => 'gray',
                        'group' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('label')
                    ->label('Label')
                    ->searchable()
                    ->sortable()
                    ->description(fn (RajaMenuItem $record): ?string => $record->key),

                Tables\Columns\IconColumn::make('icon')
                    ->label('Icon')
                    ->icon(fn (RajaMenuItem $record): ?string => $record->icon)
                    ->default('heroicon-o-document'),

                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->badge()
                    ->color('gray')
                    ->sortable(),

                Tables\Columns\TextColumn::make('parent.label')
                    ->label('Parent')
                    ->sortable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Order')
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_visible')
                    ->label('Visible')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_auto_discovered')
                    ->label('Auto')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('panel_id')
                    ->label('Panel')
                    ->options(fn () => collect(MenuDiscoveryService::getAvailablePanels())->pluck('name', 'id')),

                SelectFilter::make('type')
                    ->label('Type')
                    ->options([
                        'resource' => 'Resource',
                        'page' => 'Page',
                        'widget' => 'Widget',
                        'navigation' => 'Navigation',
                        'separator' => 'Separator',
                        'group' => 'Group',
                    ]),

                SelectFilter::make('group')
                    ->label('Group')
                    ->options(fn () => RajaMenuItem::distinct()->pluck('group', 'group')->filter()->toArray()),

                TernaryFilter::make('is_active')
                    ->label('Active'),

                TernaryFilter::make('is_visible')
                    ->label('Visible'),

                TernaryFilter::make('is_auto_discovered')
                    ->label('Auto Discovered'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn (RajaMenuItem $record) => !$record->is_auto_discovered),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => auth()->user()?->can('delete_raja_menu_item')),
                ]),
            ])
            ->defaultSort('panel_id')
            ->defaultSort('group')
            ->defaultSort('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRajaMenuItems::route('/'),
            'create' => Pages\CreateRajaMenuItem::route('/create'),
            'view' => Pages\ViewRajaMenuItem::route('/{record}'),
            'edit' => Pages\EditRajaMenuItem::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
