<?php

namespace Modules\RajaMenu\Filament\Resources\RajaMenuItemResource\Pages;

use Modules\RajaMenu\Filament\Resources\RajaMenuItemResource;
use Filament\Resources\Pages\CreateRecord;
use Modules\RajaMenu\Models\RajaMenuItem;

class CreateRajaMenuItem extends CreateRecord
{
    protected static string $resource = RajaMenuItemResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set default sort order if not provided
        if (!isset($data['sort_order']) || $data['sort_order'] === 0) {
            $data['sort_order'] = RajaMenuItem::getNextSortOrder($data['parent_id'] ?? null);
        }

        // Generate key if not provided
        if (empty($data['key'])) {
            $data['key'] = 'custom_' . \Str::slug($data['label']) . '_' . time();
        }

        return $data;
    }
}
