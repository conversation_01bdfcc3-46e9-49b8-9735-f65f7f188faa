<?php


use Illuminate\Support\Facades\Route;





Route::get('/cetak/struk/{penjualanId}', [App\Http\Controllers\CetakController::class, 'cetakStruk'])
    ->name('cetak.struk');

Route::post('/cetak/struk/template', [App\Http\Controllers\CetakController::class, 'cetakStrukDenganTemplate'])
    ->name('cetak.struk.template');


// Route untuk halaman depan
Route::get('/', [App\Http\Controllers\CmsController::class, 'beranda'])->name('home');
Route::get('/artikel', [App\Http\Controllers\CmsController::class, 'daftarArtikel'])->name('artikel.daftar');
Route::get('/artikel/{slug}', [App\Http\Controllers\CmsController::class, 'detailArtikel'])->name('artikel.detail');
Route::get('/event/{slug}', [App\Http\Controllers\CmsController::class, 'detailEvent'])->name('event.detail');
Route::get('/halaman/{slug}', [App\Http\Controllers\CmsController::class, 'halaman'])->name('halaman');
Route::get('/acara', [App\Http\Controllers\CmsController::class, 'daftarAcara'])->name('acara.daftar');
Route::get('/acara/{slug}', [App\Http\Controllers\CmsController::class, 'detailAcara'])->name('acara.detail');

// Route untuk upload gambar TinyMCE
Route::post('/upload-tinymce', [App\Http\Controllers\TinyMceController::class, 'upload'])
    ->middleware(['auth', 'bypass.auth'])
    ->name('tinymce.upload');

// Route untuk upload gambar RajaEditor
Route::post('/upload-rajaeditor', [App\Http\Controllers\ModulUploadController::class, 'upload'])
    ->middleware(['auth'])
    ->name('rajaeditor.upload');




\Laravel\Folio\Folio::path(resource_path('views/testing'))->uri('testing');

// TIDAK PERLU MEMBUAT ROUTE UNTUK TESTING, KARENA SUDAH MEMAKAI PACKAGE FOLIO

 
Route::get('/marketplace', [App\Http\Controllers\CmsController::class, 'marketplace'])->name('marketplace');

// Route untuk testing flyout navigation
Route::get('/test-flyout', function () {
    return view('test-flyout');
})->name('test.flyout');

