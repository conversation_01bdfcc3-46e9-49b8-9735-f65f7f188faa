<?php

namespace Modules\RajaMenu\Filament\Resources\RajaMenuItemResource\Pages;

use Modules\RajaMenu\Filament\Resources\RajaMenuItemResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\RajaMenu\Services\MenuDiscoveryService;
use Filament\Notifications\Notification;

class ListRajaMenuItems extends ListRecords
{
    protected static string $resource = RajaMenuItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('discover')
                ->label('Auto Discovery')
                ->icon('heroicon-o-magnifying-glass')
                ->color('success')
                ->form([
                    \Filament\Forms\Components\Select::make('panel_id')
                        ->label('Panel')
                        ->options(fn () => collect(MenuDiscoveryService::getAvailablePanels())->pluck('name', 'id'))
                        ->required()
                        ->default('admin'),
                ])
                ->action(function (array $data) {
                    try {
                        $discovered = MenuDiscoveryService::discoverForPanel($data['panel_id']);
                        
                        Notification::make()
                            ->title('Auto Discovery Berhasil')
                            ->body(sprintf('Ditemukan %d menu items untuk panel %s', count($discovered), $data['panel_id']))
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Auto Discovery Gagal')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\CreateAction::make(),
        ];
    }
}
