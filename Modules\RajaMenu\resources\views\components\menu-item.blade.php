@props(['item', 'level' => 0])

<div 
    class="menu-item-card menu-item-depth-{{ $level }} menu-item-type-{{ $item['type'] }} bg-white rounded-lg border border-gray-200 p-4 mb-2"
    data-item-id="{{ $item['id'] }}"
    data-parent-id="{{ $item['parent_id'] ?? '' }}"
>
    <div class="flex items-center justify-between">
        {{-- Left side: Drag handle, icon, label --}}
        <div class="flex items-center space-x-3">
            {{-- Drag Handle --}}
            <div class="drag-handle text-gray-400 hover:text-gray-600">
                <x-filament::icon icon="heroicon-o-bars-3" class="w-5 h-5" />
            </div>
            
            {{-- Type Indicator --}}
            <div class="flex-shrink-0">
                @switch($item['type'])
                    @case('resource')
                        <x-filament::badge color="success" size="sm">Resource</x-filament::badge>
                        @break
                    @case('page')
                        <x-filament::badge color="primary" size="sm">Page</x-filament::badge>
                        @break
                    @case('widget')
                        <x-filament::badge color="purple" size="sm">Widget</x-filament::badge>
                        @break
                    @case('navigation')
                        <x-filament::badge color="warning" size="sm">Navigation</x-filament::badge>
                        @break
                    @case('separator')
                        <x-filament::badge color="gray" size="sm">Separator</x-filament::badge>
                        @break
                    @case('group')
                        <x-filament::badge color="danger" size="sm">Group</x-filament::badge>
                        @break
                @endswitch
            </div>
            
            {{-- Icon --}}
            @if($item['icon'])
                <div class="flex-shrink-0">
                    <x-filament::icon :icon="$item['icon']" class="w-5 h-5 text-gray-500" />
                </div>
            @endif
            
            {{-- Label and Info --}}
            <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2">
                    <h4 class="text-sm font-medium text-gray-900 truncate">
                        {{ $item['label'] }}
                    </h4>
                    
                    @if($item['is_auto_discovered'])
                        <x-filament::badge color="info" size="xs">Auto</x-filament::badge>
                    @endif
                    
                    @if($item['group'])
                        <x-filament::badge color="gray" size="xs">{{ $item['group'] }}</x-filament::badge>
                    @endif
                </div>
                
                <div class="flex items-center space-x-4 mt-1">
                    @if($item['url'])
                        <a href="{{ $item['url'] }}" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 truncate max-w-xs">
                            {{ $item['url'] }}
                        </a>
                    @endif
                    
                    @if($item['key'] && $item['key'] !== $item['label'])
                        <span class="text-xs text-gray-500 truncate max-w-xs">
                            {{ $item['key'] }}
                        </span>
                    @endif
                </div>
            </div>
        </div>
        
        {{-- Right side: Status toggles and actions --}}
        <div class="flex items-center space-x-2">
            {{-- Children Count --}}
            @if($item['has_children'])
                <x-filament::badge color="gray" size="sm">
                    {{ count($item['children']) }} children
                </x-filament::badge>
            @endif
            
            {{-- Sort Order --}}
            <span class="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                #{{ $item['sort_order'] }}
            </span>
            
            {{-- Visibility Toggle --}}
            <button 
                wire:click="toggleVisibility({{ $item['id'] }})"
                class="p-1 rounded hover:bg-gray-100"
                title="Toggle Visibility"
            >
                @if($item['is_visible'])
                    <x-filament::icon icon="heroicon-o-eye" class="w-4 h-4 text-green-600" />
                @else
                    <x-filament::icon icon="heroicon-o-eye-slash" class="w-4 h-4 text-gray-400" />
                @endif
            </button>
            
            {{-- Active Toggle --}}
            <button 
                wire:click="toggleActive({{ $item['id'] }})"
                class="p-1 rounded hover:bg-gray-100"
                title="Toggle Active"
            >
                @if($item['is_active'])
                    <x-filament::icon icon="heroicon-o-check-circle" class="w-4 h-4 text-green-600" />
                @else
                    <x-filament::icon icon="heroicon-o-x-circle" class="w-4 h-4 text-red-400" />
                @endif
            </button>
            
            {{-- Actions Dropdown --}}
            <div x-data="{ open: false }" class="relative">
                <button 
                    @click="open = !open"
                    class="p-1 rounded hover:bg-gray-100"
                    title="Actions"
                >
                    <x-filament::icon icon="heroicon-o-ellipsis-vertical" class="w-4 h-4 text-gray-500" />
                </button>
                
                <div 
                    x-show="open" 
                    @click.away="open = false"
                    x-transition
                    class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10"
                >
                    <div class="py-1">
                        @if($item['url'])
                            <a 
                                href="{{ $item['url'] }}" 
                                target="_blank"
                                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                                <x-filament::icon icon="heroicon-o-arrow-top-right-on-square" class="w-4 h-4 inline mr-2" />
                                Open Link
                            </a>
                        @endif
                        
                        <button 
                            wire:click="editMenuItem({{ $item['id'] }})"
                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                            <x-filament::icon icon="heroicon-o-pencil" class="w-4 h-4 inline mr-2" />
                            Edit
                        </button>
                        
                        <button 
                            wire:click="duplicateMenuItem({{ $item['id'] }})"
                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                            <x-filament::icon icon="heroicon-o-document-duplicate" class="w-4 h-4 inline mr-2" />
                            Duplicate
                        </button>
                        
                        @if(!$item['is_auto_discovered'])
                            <hr class="my-1">
                            <button 
                                wire:click="deleteMenuItem({{ $item['id'] }})"
                                wire:confirm="Apakah Anda yakin ingin menghapus menu item ini?"
                                class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                            >
                                <x-filament::icon icon="heroicon-o-trash" class="w-4 h-4 inline mr-2" />
                                Delete
                            </button>
                        @else
                            <div class="px-4 py-2 text-xs text-gray-500">
                                Auto-discovered items cannot be deleted
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {{-- Children (if any) --}}
    @if(!empty($item['children']))
        <div class="mt-4 space-y-2 border-l-2 border-gray-100 pl-4">
            @foreach($item['children'] as $child)
                @include('rajamenu::components.menu-item', ['item' => $child, 'level' => $level + 1])
            @endforeach
        </div>
    @endif
</div>
