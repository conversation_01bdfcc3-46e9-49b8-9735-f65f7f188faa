{{-- Auto-Discovery Flyout Navigation untuk FilamentPHP --}}

@php
    use Modules\RajaMenu\Services\FlyoutNavigationService;
    
    // Auto-discover navigation from panel
    $discoveredNav = FlyoutNavigationService::discoverNavigationFromPanel('admin');
    $navigationItems = $discoveredNav['navigationItems'];
    $childItems = $discoveredNav['childItems'];
@endphp

<style>
/* Inline CSS untuk flyout navigation */
.auto-flyout-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.nav-group {
    position: relative;
    display: inline-block;
}

.nav-group-label {
    padding: 0.5rem 1rem;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.nav-group-label:hover {
    background-color: #f3f4f6;
    color: #f59e0b;
}

.nav-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.nav-group:hover .nav-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.nav-item {
    display: block;
    padding: 12px 16px;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
}

.nav-item:hover {
    background-color: #f3f4f6;
    color: #1f2937;
}

.nav-item.has-children::after {
    content: '›';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #9ca3af;
}

.nav-sub-dropdown {
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 180px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.nav-item:hover .nav-sub-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.nav-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    display: inline-block;
}

.nav-badge {
    background: #ef4444;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
}

.nav-type-resource { border-left: 3px solid #10b981; }
.nav-type-page { border-left: 3px solid #3b82f6; }
</style>

<nav class="auto-flyout-nav">
    @foreach($navigationItems as $groupName => $items)
        <div class="nav-group">
            <div class="nav-group-label">
                {{ $groupName }}
            </div>
            <div class="nav-dropdown">
                @php
                    // Sort items by sort order
                    $sortedItems = collect($items)->sortBy('sort')->toArray();
                @endphp
                
                @foreach($sortedItems as $item)
                    @php
                        $hasChildren = isset($childItems[$item['label']]) && count($childItems[$item['label']]) > 0;
                    @endphp

                    @if($hasChildren)
                        {{-- Parent item with children --}}
                        <div class="nav-item has-children nav-type-{{ $item['type'] }}">
                            @if($item['icon'])
                                <x-filament::icon :icon="$item['icon']" class="nav-icon" />
                            @endif
                            {{ $item['label'] }}
                            @if($item['badge'])
                                <span class="nav-badge">{{ $item['badge'] }}</span>
                            @endif
                            
                            <div class="nav-sub-dropdown">
                                @php
                                    $sortedChildren = collect($childItems[$item['label']])->sortBy('sort')->toArray();
                                @endphp
                                
                                @foreach($sortedChildren as $childItem)
                                    <a href="{{ $childItem['url'] }}" class="nav-item nav-type-{{ $childItem['type'] }}">
                                        @if($childItem['icon'])
                                            <x-filament::icon :icon="$childItem['icon']" class="nav-icon" />
                                        @endif
                                        {{ $childItem['label'] }}
                                        @if($childItem['badge'])
                                            <span class="nav-badge">{{ $childItem['badge'] }}</span>
                                        @endif
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @else
                        {{-- Regular item without children --}}
                        <a href="{{ $item['url'] }}" class="nav-item nav-type-{{ $item['type'] }}">
                            @if($item['icon'])
                                <x-filament::icon :icon="$item['icon']" class="nav-icon" />
                            @endif
                            {{ $item['label'] }}
                            @if($item['badge'])
                                <span class="nav-badge">{{ $item['badge'] }}</span>
                            @endif
                        </a>
                    @endif
                @endforeach
            </div>
        </div>
    @endforeach
</nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced hover behavior untuk smooth UX
    const navGroups = document.querySelectorAll('.nav-group');
    
    navGroups.forEach(group => {
        const dropdown = group.querySelector('.nav-dropdown');
        let hoverTimeout;
        
        group.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
        });
        
        group.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(() => {
                // Optional: add fade out effect
            }, 100);
        });
    });
    
    // Handle sub-dropdown behavior
    const navItems = document.querySelectorAll('.nav-item.has-children');
    
    navItems.forEach(item => {
        const subDropdown = item.querySelector('.nav-sub-dropdown');
        let subHoverTimeout;
        
        item.addEventListener('mouseenter', function() {
            clearTimeout(subHoverTimeout);
        });
        
        item.addEventListener('mouseleave', function() {
            subHoverTimeout = setTimeout(() => {
                // Optional: add fade out effect
            }, 100);
        });
    });
});
</script>

{{-- Debug info (remove in production) --}}
@if(config('app.debug'))
    <div style="position: fixed; bottom: 10px; right: 10px; background: #1f2937; color: white; padding: 10px; border-radius: 6px; font-size: 12px; z-index: 9999;">
        <strong>Auto-Discovery Debug:</strong><br>
        Groups: {{ count($navigationItems) }}<br>
        @foreach($navigationItems as $group => $items)
            {{ $group }}: {{ count($items) }} items<br>
        @endforeach
        Children: {{ count($childItems) }} parent items
    </div>
@endif
