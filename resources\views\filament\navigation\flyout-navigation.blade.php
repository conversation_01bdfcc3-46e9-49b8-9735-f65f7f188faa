{{-- Custom Flyout Navigation untuk FilamentPHP --}}

<nav class="fi-topbar-nav">
    <div class="flex items-center space-x-1">
        @foreach($navigationItems as $groupName => $items)
            <div class="fi-topbar-nav-group">
                {{-- Group Label --}}
                <div class="fi-topbar-nav-group-label">
                    {{ $groupName }}
                </div>

                {{-- Dropdown Container --}}
                <div class="fi-dropdown">
                    @foreach($items as $item)
                        @php
                            $itemLabel = $item['label'];
                            $itemIcon = $item['icon'] ?? null;
                            $itemUrl = $item['url'] ?? '#';
                            $hasChildren = isset($childItems[$itemLabel]) && count($childItems[$itemLabel]) > 0;
                        @endphp

                        @if($hasChildren)
                            {{-- Parent item with children --}}
                            <div class="fi-dropdown-item has-children">
                                <div class="flex items-center">
                                    @if($itemIcon)
                                        <x-filament::icon
                                            :icon="$itemIcon"
                                            class="fi-icon"
                                        />
                                    @endif
                                    <span>{{ $itemLabel }}</span>
                                </div>

                                {{-- Sub-dropdown --}}
                                <div class="fi-dropdown-sub">
                                    @foreach($childItems[$itemLabel] as $childItem)
                                        <a
                                            href="{{ $childItem['url'] ?? '#' }}"
                                            class="fi-dropdown-item"
                                        >
                                            @if($childItem['icon'] ?? null)
                                                <x-filament::icon
                                                    :icon="$childItem['icon']"
                                                    class="fi-icon"
                                                />
                                            @endif
                                            {{ $childItem['label'] }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            {{-- Regular item without children --}}
                            <a
                                href="{{ $itemUrl }}"
                                class="fi-dropdown-item"
                            >
                                @if($itemIcon)
                                    <x-filament::icon
                                        :icon="$itemIcon"
                                        class="fi-icon"
                                    />
                                @endif
                                {{ $itemLabel }}
                            </a>
                        @endif
                    @endforeach
                </div>
            </div>
        @endforeach
    </div>
</nav>

<style>
    /* Include the flyout navigation CSS */
    @import url('{{ asset('css/filament-flyout-navigation.css') }}');
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced hover behavior for better UX
    const navGroups = document.querySelectorAll('.fi-topbar-nav-group');
    
    navGroups.forEach(group => {
        const dropdown = group.querySelector('.fi-dropdown');
        let hoverTimeout;
        
        group.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
            dropdown.style.display = 'block';
            setTimeout(() => {
                dropdown.classList.add('show');
            }, 10);
        });
        
        group.addEventListener('mouseleave', function() {
            dropdown.classList.remove('show');
            hoverTimeout = setTimeout(() => {
                dropdown.style.display = 'none';
            }, 300);
        });
    });
    
    // Handle sub-dropdown behavior
    const dropdownItems = document.querySelectorAll('.fi-dropdown-item.has-children');
    
    dropdownItems.forEach(item => {
        const subDropdown = item.querySelector('.fi-dropdown-sub');
        let subHoverTimeout;
        
        item.addEventListener('mouseenter', function() {
            clearTimeout(subHoverTimeout);
            if (subDropdown) {
                subDropdown.style.display = 'block';
                setTimeout(() => {
                    subDropdown.classList.add('show');
                }, 10);
            }
        });
        
        item.addEventListener('mouseleave', function() {
            if (subDropdown) {
                subDropdown.classList.remove('show');
                subHoverTimeout = setTimeout(() => {
                    subDropdown.style.display = 'none';
                }, 200);
            }
        });
    });
});
</script>
