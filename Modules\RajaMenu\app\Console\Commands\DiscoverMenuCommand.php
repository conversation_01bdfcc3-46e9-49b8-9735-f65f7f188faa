<?php

namespace Modules\RajaMenu\Console\Commands;

use Illuminate\Console\Command;
use Mo<PERSON>les\RajaMenu\Services\MenuDiscoveryService;

class DiscoverMenuCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'rajamenu:discover 
                            {panel? : The panel ID to discover (default: all panels)}
                            {--reset : Reset all menu items before discovery}
                            {--clear-auto : Clear only auto-discovered items}';

    /**
     * The console command description.
     */
    protected $description = 'Discover and sync menu items for FilamentPHP panels';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $panelId = $this->argument('panel');
        $reset = $this->option('reset');
        $clearAuto = $this->option('clear-auto');

        try {
            $availablePanels = MenuDiscoveryService::getAvailablePanels();
            
            if (empty($availablePanels)) {
                $this->error('No FilamentPHP panels found.');
                return self::FAILURE;
            }

            $panelsToProcess = $panelId ? [$panelId] : array_keys($availablePanels);

            foreach ($panelsToProcess as $currentPanelId) {
                if (!isset($availablePanels[$currentPanelId])) {
                    $this->error("Panel '{$currentPanelId}' not found.");
                    continue;
                }

                $this->info("Processing panel: {$currentPanelId}");

                // Reset or clear if requested
                if ($reset) {
                    $deleted = MenuDiscoveryService::resetPanel($currentPanelId);
                    $this->info("  Deleted {$deleted} existing menu items");
                } elseif ($clearAuto) {
                    $deleted = MenuDiscoveryService::clearAutoDiscovered($currentPanelId);
                    $this->info("  Deleted {$deleted} auto-discovered menu items");
                }

                // Discover menu items
                $this->info("  Discovering menu items...");
                $discovered = MenuDiscoveryService::discoverForPanel($currentPanelId);

                // Show statistics
                $stats = MenuDiscoveryService::getPanelStatistics($currentPanelId);
                
                $this->info("  Discovery completed:");
                $this->line("    - Resources: " . ($stats['by_type']['resource'] ?? 0));
                $this->line("    - Pages: " . ($stats['by_type']['page'] ?? 0));
                $this->line("    - Widgets: " . ($stats['by_type']['widget'] ?? 0));
                $this->line("    - Total discovered: " . count($discovered));
                $this->line("    - Total active: " . $stats['active']);
                $this->line("    - Total visible: " . $stats['visible']);

                $this->newLine();
            }

            $this->info('Menu discovery completed successfully!');
            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Discovery failed: ' . $e->getMessage());
            
            if ($this->getOutput()->isVerbose()) {
                $this->error($e->getTraceAsString());
            }
            
            return self::FAILURE;
        }
    }
}
