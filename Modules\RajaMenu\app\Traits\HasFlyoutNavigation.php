<?php

namespace Modules\RajaMenu\Traits;

use Filament\Support\Assets\Css;
use Modules\RajaMenu\Services\FlyoutNavigationService;

trait HasFlyoutNavigation
{
    /**
     * Apply flyout navigation to panel
     */
    protected function applyFlyoutNavigation($panel)
    {
        return $panel
            ->renderHook(
                'panels::topbar.start',
                fn() => view('rajamenu::navigation.simple-flyout')
            )
            ->assets([
                Css::make('rajamenu-flyout-navigation', module_path('RajaMenu', 'resources/assets/css/flyout-navigation.css')),
            ]);
    }

    /**
     * Apply complex flyout navigation with data binding
     */
    protected function applyComplexFlyoutNavigation($panel)
    {
        return $panel
            ->renderHook(
                'panels::topbar.start',
                fn() => view('rajamenu::navigation.flyout-navigation', [
                    'navigationItems' => $this->getFlyoutNavigationItems(),
                    'childItems' => $this->getFlyoutChildNavigationItems()
                ])
            )
            ->assets([
                Css::make('rajamenu-flyout-navigation', module_path('RajaMenu', 'resources/assets/css/flyout-navigation.css')),
            ]);
    }

    /**
     * Get navigation items for flyout
     */
    protected function getFlyoutNavigationItems(): array
    {
        return FlyoutNavigationService::getNavigationItems();
    }

    /**
     * Get child navigation items for flyout
     */
    protected function getFlyoutChildNavigationItems(): array
    {
        return FlyoutNavigationService::getChildNavigationItems();
    }

    /**
     * Get all navigation items (FilamentPHP NavigationItem objects)
     */
    protected function getAllFlyoutNavigationItems(): array
    {
        return FlyoutNavigationService::getAllNavigationItems();
    }
}
