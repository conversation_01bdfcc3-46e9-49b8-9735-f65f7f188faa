<?php

namespace Modules\RajaMenu\Services;

use Filament\Facades\Filament;
use Filament\Panel;
use Modules\RajaMenu\Models\RajaMenuItem;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use ReflectionClass;

class MenuDiscoveryService
{
    /**
     * Discover and sync menu items for a specific panel
     */
    public static function discoverForPanel(string $panelId): array
    {
        $panel = Filament::getPanel($panelId);
        $discovered = [];

        // Discover resources
        $discovered = array_merge($discovered, static::discoverResources($panel, $panelId));

        // Discover pages
        $discovered = array_merge($discovered, static::discoverPages($panel, $panelId));

        // Discover widgets
        $discovered = array_merge($discovered, static::discoverWidgets($panel, $panelId));

        // Sync with database
        static::syncDiscoveredItems($panelId, $discovered);

        return $discovered;
    }

    /**
     * Discover resources for panel
     */
    protected static function discoverResources(Panel $panel, string $panelId): array
    {
        $resources = [];
        
        foreach ($panel->getResources() as $resource) {
            try {
                $reflection = new ReflectionClass($resource);
                
                // Skip if resource doesn't have required methods
                if (!method_exists($resource, 'getNavigationLabel') || 
                    !method_exists($resource, 'getNavigationIcon')) {
                    continue;
                }

                $label = $resource::getNavigationLabel();
                $icon = $resource::getNavigationIcon();
                $group = method_exists($resource, 'getNavigationGroup') ? $resource::getNavigationGroup() : null;
                $sort = method_exists($resource, 'getNavigationSort') ? $resource::getNavigationSort() : 0;
                $badge = method_exists($resource, 'getNavigationBadge') ? $resource::getNavigationBadge() : null;

                // Check if resource should be hidden
                $shouldRegisterNavigation = method_exists($resource, 'shouldRegisterNavigation') 
                    ? $resource::shouldRegisterNavigation() 
                    : true;

                if (!$shouldRegisterNavigation) {
                    continue;
                }

                $resources[] = [
                    'type' => 'resource',
                    'key' => $resource,
                    'label' => $label,
                    'icon' => $icon,
                    'group' => $group,
                    'sort_order' => $sort ?? 0,
                    'metadata' => [
                        'badge' => $badge,
                        'model' => method_exists($resource, 'getModel') ? $resource::getModel() : null,
                        'file_path' => $reflection->getFileName(),
                    ],
                ];
            } catch (\Exception $e) {
                // Skip problematic resources
                continue;
            }
        }

        return $resources;
    }

    /**
     * Discover pages for panel
     */
    protected static function discoverPages(Panel $panel, string $panelId): array
    {
        $pages = [];
        
        foreach ($panel->getPages() as $page) {
            try {
                $reflection = new ReflectionClass($page);
                
                // Skip if page doesn't have required methods
                if (!method_exists($page, 'getNavigationLabel')) {
                    continue;
                }

                $label = $page::getNavigationLabel();
                $icon = method_exists($page, 'getNavigationIcon') ? $page::getNavigationIcon() : 'heroicon-o-document';
                $group = method_exists($page, 'getNavigationGroup') ? $page::getNavigationGroup() : null;
                $sort = method_exists($page, 'getNavigationSort') ? $page::getNavigationSort() : 0;

                // Check if page should be hidden
                $shouldRegisterNavigation = method_exists($page, 'shouldRegisterNavigation') 
                    ? $page::shouldRegisterNavigation() 
                    : true;

                if (!$shouldRegisterNavigation) {
                    continue;
                }

                $pages[] = [
                    'type' => 'page',
                    'key' => $page,
                    'label' => $label,
                    'icon' => $icon,
                    'group' => $group,
                    'sort_order' => $sort ?? 0,
                    'metadata' => [
                        'file_path' => $reflection->getFileName(),
                    ],
                ];
            } catch (\Exception $e) {
                // Skip problematic pages
                continue;
            }
        }

        return $pages;
    }

    /**
     * Discover widgets for panel
     */
    protected static function discoverWidgets(Panel $panel, string $panelId): array
    {
        $widgets = [];
        
        foreach ($panel->getWidgets() as $widget) {
            try {
                $reflection = new ReflectionClass($widget);
                
                // Only include widgets that have navigation properties
                if (!method_exists($widget, 'getNavigationLabel')) {
                    continue;
                }

                $label = $widget::getNavigationLabel();
                $icon = method_exists($widget, 'getNavigationIcon') ? $widget::getNavigationIcon() : 'heroicon-o-chart-bar';
                $group = method_exists($widget, 'getNavigationGroup') ? $widget::getNavigationGroup() : 'Widgets';
                $sort = method_exists($widget, 'getNavigationSort') ? $widget::getNavigationSort() : 0;

                $widgets[] = [
                    'type' => 'widget',
                    'key' => $widget,
                    'label' => $label,
                    'icon' => $icon,
                    'group' => $group,
                    'sort_order' => $sort ?? 0,
                    'metadata' => [
                        'file_path' => $reflection->getFileName(),
                    ],
                ];
            } catch (\Exception $e) {
                // Skip problematic widgets
                continue;
            }
        }

        return $widgets;
    }

    /**
     * Sync discovered items with database
     */
    protected static function syncDiscoveredItems(string $panelId, array $discovered): void
    {
        $discoveredKeys = collect($discovered)->pluck('key')->toArray();

        // Mark existing auto-discovered items as inactive if they're no longer discovered
        RajaMenuItem::forPanel($panelId)
            ->where('is_auto_discovered', true)
            ->whereNotIn('key', $discoveredKeys)
            ->update(['is_active' => false]);

        // Create or update discovered items
        foreach ($discovered as $item) {
            RajaMenuItem::updateOrCreate(
                [
                    'panel_id' => $panelId,
                    'key' => $item['key'],
                ],
                [
                    'type' => $item['type'],
                    'label' => $item['label'],
                    'icon' => $item['icon'],
                    'group' => $item['group'],
                    'sort_order' => $item['sort_order'],
                    'is_auto_discovered' => true,
                    'is_active' => true,
                    'is_visible' => true,
                    'metadata' => $item['metadata'] ?? [],
                ]
            );
        }
    }

    /**
     * Get all available panels
     */
    public static function getAvailablePanels(): array
    {
        $panels = [];
        
        foreach (Filament::getPanels() as $panel) {
            $panels[$panel->getId()] = [
                'id' => $panel->getId(),
                'name' => $panel->getId(),
                'path' => $panel->getPath(),
            ];
        }

        return $panels;
    }

    /**
     * Get navigation groups for panel
     */
    public static function getNavigationGroups(string $panelId): array
    {
        try {
            $panel = Filament::getPanel($panelId);
            $groups = [];

            // Get groups from panel configuration
            foreach ($panel->getNavigationGroups() as $group) {
                $groups[] = [
                    'key' => $group->getLabel(),
                    'label' => $group->getLabel(),
                    'icon' => $group->getIcon(),
                    'collapsed' => $group->isCollapsed(),
                ];
            }

            // Get groups from discovered items
            $discoveredGroups = RajaMenuItem::forPanel($panelId)
                ->whereNotNull('group')
                ->distinct()
                ->pluck('group')
                ->filter()
                ->map(function ($group) {
                    return [
                        'key' => $group,
                        'label' => $group,
                        'icon' => null,
                        'collapsed' => false,
                    ];
                })
                ->toArray();

            // Merge and deduplicate
            $allGroups = collect($groups)
                ->merge($discoveredGroups)
                ->unique('key')
                ->values()
                ->toArray();

            return $allGroups;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get statistics for panel
     */
    public static function getPanelStatistics(string $panelId): array
    {
        $total = RajaMenuItem::forPanel($panelId)->count();
        $active = RajaMenuItem::forPanel($panelId)->active()->count();
        $visible = RajaMenuItem::forPanel($panelId)->visible()->count();
        $autoDiscovered = RajaMenuItem::forPanel($panelId)->where('is_auto_discovered', true)->count();
        $manual = RajaMenuItem::forPanel($panelId)->where('is_auto_discovered', false)->count();

        $byType = RajaMenuItem::forPanel($panelId)
            ->selectRaw('type, count(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        return [
            'total' => $total,
            'active' => $active,
            'visible' => $visible,
            'auto_discovered' => $autoDiscovered,
            'manual' => $manual,
            'by_type' => $byType,
        ];
    }

    /**
     * Clear all auto-discovered items for panel
     */
    public static function clearAutoDiscovered(string $panelId): int
    {
        return RajaMenuItem::forPanel($panelId)
            ->where('is_auto_discovered', true)
            ->delete();
    }

    /**
     * Reset all items for panel
     */
    public static function resetPanel(string $panelId): int
    {
        return RajaMenuItem::forPanel($panelId)->delete();
    }
}
