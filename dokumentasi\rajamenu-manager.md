# RajaMenu Manager - Menu Management System

## 📋 **Overview**

RajaMenu Manager adalah sistem manajemen menu lengkap untuk FilamentPHP dengan fitur:
- ✅ **Drag & Drop Ordering** - Reorder menu dengan drag & drop
- ✅ **Auto Discovery** - Otomatis menemukan resources, pages, widgets
- ✅ **Per Panel Management** - Kelola menu per panel (admin, member, dll)
- ✅ **Hierarchical Structure** - Support parent-child menu
- ✅ **Custom Menu Items** - Tambah menu custom dengan URL
- ✅ **Permission Management** - Kontrol akses per menu item

## 🎯 **Fitur Utama**

### ✅ **Menu Manager Page**
- **URL:** `/admin/menu-manager`
- **Drag & Drop:** Reorder menu items dengan mudah
- **Panel Selector:** Switch antar panel (admin, member, dll)
- **Statistics:** Lihat statistik menu per panel
- **Real-time Updates:** Perubahan langsung tersimpan

### ✅ **Auto Discovery System**
- **Command:** `php artisan rajamenu:discover`
- **Automatic Detection:** Resources, Pages, Widgets
- **Smart Sync:** Update otomatis tanpa duplikasi
- **Metadata Storage:** Simpan informasi tambahan

### ✅ **Menu Item Resource**
- **CRUD Operations:** Create, Read, Update, Delete
- **Bulk Actions:** Operasi massal
- **Advanced Filtering:** Filter by panel, type, group
- **Permission Control:** Manage access permissions

## 📁 **Struktur Database**

### **Table: `raja_menu_items`**
```sql
- id (primary key)
- panel_id (string) - Panel identifier
- type (string) - resource, page, widget, navigation, separator, group
- key (string, unique) - Unique identifier
- label (string) - Display label
- icon (string) - Icon name
- url (string) - Custom URL
- group (string) - Navigation group
- parent_id (foreign key) - Parent menu item
- sort_order (integer) - Sort order
- is_active (boolean) - Active status
- is_visible (boolean) - Visibility
- is_auto_discovered (boolean) - Auto-discovered flag
- permissions (json) - Required permissions
- metadata (json) - Additional data
```

## 🚀 **Instalasi & Setup**

### 1. **Aktifkan Modul**
```bash
php artisan module:enable RajaMenu
```

### 2. **Jalankan Migration**
```bash
php artisan migrate --path=Modules/RajaMenu/database/migrations
```

### 3. **Auto Discovery**
```bash
# Discover semua panel
php artisan rajamenu:discover

# Discover panel tertentu
php artisan rajamenu:discover admin

# Reset dan discover ulang
php artisan rajamenu:discover admin --reset
```

### 4. **Akses Menu Manager**
- Buka admin panel: `/admin`
- Navigasi ke: **RajaMenu > Menu Manager**

## 🎨 **Cara Penggunaan**

### **1. Auto Discovery**
```bash
# Discover menu items untuk panel admin
php artisan rajamenu:discover admin

# Output:
# Processing panel: admin
#   Discovering menu items...
#   Discovery completed:
#     - Resources: 9
#     - Pages: 4
#     - Widgets: 0
#     - Total discovered: 13
```

### **2. Menu Manager Interface**

#### **Panel Selection**
- Pilih panel dari dropdown
- Statistik otomatis update
- Menu items filtered per panel

#### **Drag & Drop Ordering**
- Drag handle (☰) untuk reorder
- Support nested hierarchy
- Real-time save

#### **Menu Item Actions**
- **👁️ Visibility Toggle** - Show/hide menu
- **✅ Active Toggle** - Enable/disable menu
- **⋮ Actions Menu:**
  - Open Link (jika ada URL)
  - Edit menu item
  - Duplicate menu item
  - Delete (hanya custom items)

### **3. Tambah Menu Custom**

#### **Via Menu Manager**
1. Click **"Tambah Menu Custom"**
2. Isi form:
   - **Type:** Navigation Link, Separator, Group Header
   - **Label:** Nama menu
   - **Icon:** heroicon-o-document
   - **URL:** Link tujuan
   - **Group:** Kategori menu
   - **Parent:** Parent menu (optional)
   - **Permissions:** JSON array permissions

#### **Via Resource**
1. Navigasi ke **RajaMenu > Menu Items**
2. Click **"Create"**
3. Isi form lengkap
4. Save

### **4. Permission Management**

#### **Format Permissions**
```json
["permission1", "permission2"]
```

#### **Contoh Permissions**
```json
["view_users", "manage_settings"]
```

## 🔧 **Advanced Configuration**

### **Custom Menu Types**
```php
// Di MenuDiscoveryService
'navigation' => 'Navigation Link',
'separator' => 'Separator',
'group' => 'Group Header',
'resource' => 'Resource',
'page' => 'Page',
'widget' => 'Widget',
```

### **Panel Configuration**
```php
// config/rajamenu.php
'panels' => [
    'admin' => [
        'enabled' => true,
        'hook' => 'panels::topbar.start',
        'view' => 'rajamenu::navigation.simple-flyout',
    ],
    'member' => [
        'enabled' => true,
        'hook' => 'panels::sidebar.start',
        'view' => 'rajamenu::navigation.sidebar',
    ],
],
```

### **Auto Discovery Settings**
```php
'auto_discovery' => [
    'enabled' => true,
    'panels' => ['admin', 'member'],
    'resources' => true,
    'pages' => true,
    'widgets' => true,
],
```

## 📊 **Statistics & Monitoring**

### **Panel Statistics**
- **Total Items:** Jumlah total menu items
- **Active:** Menu items yang aktif
- **Visible:** Menu items yang visible
- **Auto-discovered:** Items dari auto discovery
- **Manual:** Items yang dibuat manual
- **Groups:** Jumlah navigation groups

### **Type Distribution**
- **Resources:** FilamentPHP resources
- **Pages:** FilamentPHP pages
- **Widgets:** FilamentPHP widgets
- **Navigation:** Custom navigation links
- **Separators:** Menu separators
- **Groups:** Group headers

## 🔄 **Workflow Management**

### **Development Workflow**
1. **Development:**
   ```bash
   # Develop resources/pages
   php artisan make:filament-resource Product
   
   # Auto discover
   php artisan rajamenu:discover admin
   ```

2. **Customization:**
   - Buka Menu Manager
   - Reorder dengan drag & drop
   - Toggle visibility/active
   - Add custom menu items

3. **Production:**
   ```bash
   # Deploy dan sync menu
   php artisan rajamenu:discover --reset
   ```

### **Content Management Workflow**
1. **Auto Discovery:** Jalankan discovery untuk update menu
2. **Manual Curation:** Atur visibility dan ordering
3. **Custom Items:** Tambah menu custom sesuai kebutuhan
4. **Permission Setup:** Set permissions per menu item

## 🐛 **Troubleshooting**

### **Menu Tidak Muncul**
```bash
# Check discovery
php artisan rajamenu:discover admin -v

# Check database
php artisan tinker
>>> \Modules\RajaMenu\Models\RajaMenuItem::forPanel('admin')->count()
```

### **Discovery Error**
```bash
# Reset dan discover ulang
php artisan rajamenu:discover admin --reset

# Clear cache
php artisan cache:clear
php artisan config:clear
```

### **Permission Issues**
- Check user permissions
- Verify permission format (JSON array)
- Test dengan super admin

## 📝 **API Reference**

### **Model Methods**
```php
// RajaMenuItem
$item->hasPermission()           // Check user permission
$item->getFullUrlAttribute()     // Get computed URL
$item->moveTo($order, $parent)   // Move item
$item->hasChildren()             // Check if has children

// Scopes
RajaMenuItem::forPanel('admin')  // Filter by panel
RajaMenuItem::active()           // Active items only
RajaMenuItem::visible()          // Visible items only
RajaMenuItem::root()             // Root items only
```

### **Service Methods**
```php
// MenuDiscoveryService
MenuDiscoveryService::discoverForPanel('admin')
MenuDiscoveryService::getAvailablePanels()
MenuDiscoveryService::getPanelStatistics('admin')
MenuDiscoveryService::resetPanel('admin')
```

## 🎉 **Kesimpulan**

RajaMenu Manager menyediakan:
- ✅ **Complete Menu Management** - Kelola semua aspek menu
- ✅ **Developer Friendly** - Auto discovery dan API
- ✅ **User Friendly** - Drag & drop interface
- ✅ **Flexible** - Support custom menu dan permissions
- ✅ **Scalable** - Multi-panel support

**Menu management sekarang menjadi mudah dan powerful!** 🚀
