{{-- Simple Flyout Navigation untuk FilamentPHP --}}

<style>
/* Inline CSS untuk flyout navigation */
.simple-flyout-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.nav-group {
    position: relative;
    display: inline-block;
}

.nav-group-label {
    padding: 0.5rem 1rem;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.nav-group-label:hover {
    background-color: #f3f4f6;
    color: #f59e0b;
}

.nav-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.nav-group:hover .nav-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.nav-item {
    display: block;
    padding: 12px 16px;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
}

.nav-item:hover {
    background-color: #f3f4f6;
    color: #1f2937;
}

.nav-item.has-children::after {
    content: '›';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #9ca3af;
}

.nav-sub-dropdown {
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 180px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.nav-item:hover .nav-sub-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.nav-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    display: inline-block;
}
</style>

<nav class="simple-flyout-nav">
    {{-- CMS Group --}}
    <div class="nav-group">
        <div class="nav-group-label">
            Cms
        </div>
        <div class="nav-dropdown">
            {{-- Content Parent --}}
            <div class="nav-item has-children">
                <span class="nav-icon">📚</span>
                Content
                <div class="nav-sub-dropdown">
                    <a href="{{ \App\Filament\Resources\CmsResource::getUrl('index') }}" class="nav-item">
                        <span class="nav-icon">📄</span>
                        Konten
                    </a>
                    <a href="{{ \App\Filament\Pages\Cms\Kategori::getUrl() }}" class="nav-item">
                        <span class="nav-icon">📰</span>
                        Artikel
                    </a>
                    <a href="#" class="nav-item">
                        <span class="nav-icon">📷</span>
                        Media Library
                    </a>
                    <a href="{{ \App\Filament\Pages\Cms\Kategori::getUrl() }}" class="nav-item">
                        <span class="nav-icon">🏷️</span>
                        Kategori artikel
                    </a>
                    <a href="{{ \App\Filament\Resources\MenuWebsiteResource::getUrl('index') }}" class="nav-item">
                        <span class="nav-icon">📋</span>
                        Menu
                    </a>
                    <a href="{{ \App\Filament\Resources\CmsResource::getUrl('tema') }}" class="nav-item">
                        <span class="nav-icon">🎨</span>
                        Tema
                    </a>
                </div>
            </div>
        </div>
    </div>

    {{-- System Group --}}
    <div class="nav-group">
        <div class="nav-group-label">
            System
        </div>
        <div class="nav-dropdown">
            {{-- Data Management Parent --}}
            <div class="nav-item has-children">
                <span class="nav-icon">🗄️</span>
                Data Management
                <div class="nav-sub-dropdown">
                    <a href="{{ \App\Filament\Resources\KategoriResource::getUrl('index') }}" class="nav-item">
                        <span class="nav-icon">📝</span>
                        Seluruh kategori
                    </a>
                    <a href="{{ \App\Filament\Resources\MetodePembayaranUtamaResource::getUrl('index') }}" class="nav-item">
                        <span class="nav-icon">💳</span>
                        Metode pembayaran
                    </a>
                    <a href="{{ \App\Filament\Pages\Data::getUrl() }}" class="nav-item">
                        <span class="nav-icon">📊</span>
                        Data
                    </a>
                </div>
            </div>
            
            {{-- System Tools Parent --}}
            <div class="nav-item has-children">
                <span class="nav-icon">🔧</span>
                System Tools
                <div class="nav-sub-dropdown">
                    <a href="{{ \App\Filament\Pages\RouteList::getUrl() }}" class="nav-item">
                        <span class="nav-icon">🗺️</span>
                        Rute
                    </a>
                    <a href="{{ \App\Filament\Pages\AplikasiRajaDesain::getUrl() }}" class="nav-item">
                        <span class="nav-icon">📱</span>
                        Aplikasi
                    </a>
                    <a href="{{ \App\Filament\Resources\KonfigResource::getUrl('index') }}" class="nav-item">
                        <span class="nav-icon">⚙️</span>
                        Konfig
                    </a>
                </div>
            </div>
        </div>
    </div>

    {{-- Pengaturan Group --}}
    <div class="nav-group">
        <div class="nav-group-label">
            Pengaturan
        </div>
        <div class="nav-dropdown">
            {{-- Business Parent --}}
            <div class="nav-item has-children">
                <span class="nav-icon">🏢</span>
                Business
                <div class="nav-sub-dropdown">
                    <a href="{{ \App\Filament\Resources\TokoResource::getUrl('index') }}" class="nav-item">
                        <span class="nav-icon">🏪</span>
                        Usaha
                    </a>
                    <a href="{{ \App\Filament\Resources\UserResource::getUrl('index') }}" class="nav-item">
                        <span class="nav-icon">👥</span>
                        Staff
                    </a>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced hover behavior untuk smooth UX
    const navGroups = document.querySelectorAll('.nav-group');
    
    navGroups.forEach(group => {
        const dropdown = group.querySelector('.nav-dropdown');
        let hoverTimeout;
        
        group.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
        });
        
        group.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(() => {
                // Optional: add fade out effect
            }, 100);
        });
    });
    
    // Handle sub-dropdown behavior
    const navItems = document.querySelectorAll('.nav-item.has-children');
    
    navItems.forEach(item => {
        const subDropdown = item.querySelector('.nav-sub-dropdown');
        let subHoverTimeout;
        
        item.addEventListener('mouseenter', function() {
            clearTimeout(subHoverTimeout);
        });
        
        item.addEventListener('mouseleave', function() {
            subHoverTimeout = setTimeout(() => {
                // Optional: add fade out effect
            }, 100);
        });
    });
});
</script>
