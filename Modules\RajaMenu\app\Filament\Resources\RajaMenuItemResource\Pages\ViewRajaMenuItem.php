<?php

namespace Modules\RajaMenu\Filament\Resources\RajaMenuItemResource\Pages;

use Modules\RajaMenu\Filament\Resources\RajaMenuItemResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewRajaMenuItem extends ViewRecord
{
    protected static string $resource = RajaMenuItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
