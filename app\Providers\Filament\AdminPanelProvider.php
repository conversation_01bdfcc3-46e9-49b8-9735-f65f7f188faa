<?php

namespace App\Providers\Filament;

use App\Filament\Resources\CmsResource;
use App\Filament\Resources\KonfigResource;
use App\Filament\Resources\MenuWebsiteResource;
// use App\Filament\Widgets\IkonWidget;
use App\Filament\Widgets\InfoWidget;
use App\Filament\Widgets\RajaDesainerWidget;
use App\Filament\Widgets\RouteWidget;
use App\Models\Konfig;
use App\Models\Toko;
use App\Models\User;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use Datlechin\FilamentMenuBuilder\FilamentMenuBuilderPlugin;
use Datlechin\FilamentMenuBuilder\MenuPanel\StaticMenuPanel;
use DutchCodingCompany\FilamentDeveloperLogins\FilamentDeveloperLoginsPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Jeffgreco13\FilamentBreezy\BreezyCore;
use ShuvroRoy\FilamentSpatieLaravelBackup\FilamentSpatieLaravelBackupPlugin;
use Solutionforest\FilamentScaffold\FilamentScaffoldPlugin;


class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {

        $webInfo = $this->webInfo();

        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->sidebarWidth('15rem')
            ->navigation(true)
            ->topNavigation()
            ->brandName($webInfo['judul'])
            ->brandLogo($webInfo['logo'])
            ->favicon($webInfo['favicon'])
            ->brandLogoHeight('3rem')
            ->renderHook(
                'panels::page.header.actions.before',
                fn() => view('topbar')
            )
            // ->renderHook(
            //     'panels::topbar.start',
            //     fn () => "tess"
            // )

            ->viteTheme('resources/css/filament/admin/theme.css')
            ->colors([
                'primary' => Color::Amber,
            ])
            ->navigationGroups([

                NavigationGroup::make('Cms'),
                NavigationGroup::make('Aplikasi'),
                NavigationGroup::make('Pengaturan'),
                NavigationGroup::make('System'),
            ])
            ->navigationItems([

                NavigationItem::make('Konten')
                    ->icon('heroicon-o-book-open')
                    ->url(fn(): string => \App\Filament\Resources\CmsResource::getUrl('index'))
                    ->group('Cms')
                    ->sort(1),
                    
 // Child items
    NavigationItem::make('Artikel')
        ->icon('heroicon-o-newspaper')
        ->url(fn(): string => ArtikelResource::getUrl('index'))
        ->group('Cms')
        ->parentItem('Konten') // Menjadi child dari 'Konten'
        ->sort(2),
                NavigationItem::make('Kategori artikel')
                    ->icon('heroicon-o-newspaper')
                    ->url(fn(): string =>  \App\Filament\Pages\Cms\Kategori::getUrl())
                    ->group('Cms')
                    ->sort(2),

                // NavigationItem::make('Widget Manager')
                // ->icon('heroicon-o-photo')
                // ->url(fn(): string =>  \App\Filament\Resources\CmsResource::getUrl('widget-manager'))
                // ->visible($this->cekAkses('admin.cms'))
                // ->group('Cms')
                // ->sort(3),
                // NavigationItem::make('Modul Manager')
                // ->icon('heroicon-o-squares-2x2')
                // ->url(fn(): string =>  \App\Filament\Resources\CmsResource::getUrl('modul-manager'))
                // ->visible($this->cekAkses('admin.cms'))
                // ->group('Cms')
                // ->sort(4),

                NavigationItem::make('menu')
                    ->icon('heroicon-o-queue-list')
                    ->url(fn(): string => \App\Filament\Resources\MenuWebsiteResource::getUrl('index'))
                    ->group('Cms')
                    ->sort(6),

                NavigationItem::make('tema')
                    ->icon('heroicon-o-paint-brush')
                    ->url(fn(): string => CmsResource::getUrl('tema'))
                    ->group('Cms')
                    ->sort(6),



                NavigationItem::make('Seluruh kategori')
                    // ->icon('tabler-list-tree')
                    ->url(fn(): string =>  \App\Filament\Resources\KategoriResource::getUrl('index'))
                    ->group('System')
                    ->sort(6),

                NavigationItem::make('Metode pembayaran')
                    // ->icon('tabler-report-money')
                    ->url(fn(): string =>   \App\Filament\Resources\MetodePembayaranUtamaResource::getUrl('index'))
                    ->group('System')
                    ->sort(6),

                NavigationItem::make('usaha')
                    // ->icon('tabler-building-bank')
                    ->url(fn(): string =>    \App\Filament\Resources\TokoResource::getUrl('index'))
                    ->group('Pengaturan')
                    ->sort(6),

                NavigationItem::make('Staff')
                    // ->icon('tabler-users-group')
                    ->url(fn(): string =>    \App\Filament\Resources\UserResource::getUrl('index'))
                    ->group('Pengaturan')
                    ->sort(6),



                // NavigationItem::make('Migrasi data')
                //     ->icon('heroicon-o-table-cells')
                //     ->url(fn(): string => \App\Filament\Pages\Migrasi::getUrl())
                //     ->group('System')
                //     ->visible($this->cekAkses('admin.pengaturan.migrasi'))
                //     ->sort(6),


                NavigationItem::make('data')
                    // ->icon('tabler-database')
                    ->url(fn(): string =>     \App\Filament\Pages\Data::getUrl())
                    ->group('System')
                    ->sort(6),

                NavigationItem::make('rute')
                    // ->icon('tabler-route-alt-left')
                    ->url(fn(): string =>    \App\Filament\Pages\RouteList::getUrl())
                    ->group('System')
                    ->sort(6),

                NavigationItem::make('aplikasi')
                    // ->icon('tabler-apps')
                    ->url(fn(): string =>    \App\Filament\Pages\AplikasiRajaDesain::getUrl())
                    ->group('System')
                    ->sort(6),

                NavigationItem::make('Konfig ')
                    // ->icon('tabler-settings-spark')
                    ->url(fn(): string =>     \App\Filament\Resources\KonfigResource::getUrl('index'))
                    ->group('System')
                    ->sort(6),

                // NavigationItem::make('Backup')
                //     ->icon('heroicon-o-cpu-chip')
                //     ->url(fn(): string =>    \App\Filament\Pages\Backup::getUrl())
                //     ->group('System')
                //     ->visible($this->cekAkses('admin.system.backup'))
                //     ->sort(6),


            ])

            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            // ->discoverResources(in: base_path('Modules/RajaJson/src/Resources'), for: 'Modules\\RajaJson\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            // ->discoverClusters(in: app_path('Filament/Clusters'), for: 'App\\Filament\\Clusters')
            ->pages([
                \App\Filament\Pages\Dashboard::class,

            ])
            // ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                // Widget ini didaftarkan tapi tidak ditampilkan di dashboard
                // Hanya digunakan di form PermissionResource dan di tempat lain
                // RouteWidget::class,
                // IkonWidget::class,
                InfoWidget::class,
                // RajaDesainerWidget::class,
            ])
            ->middleware([

                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,



            ])

            ->plugins([

                FilamentScaffoldPlugin::make(),


                FilamentDeveloperLoginsPlugin::make()
                    ->enabled()
                    ->users(fn() => User::pluck('email', 'name')->toArray()),
                FilamentMenuBuilderPlugin::make()

                    ->addLocation('website_header', 'website_header')

                    ->showCustomTextPanel()

                    ->addMenuPanels([
                        StaticMenuPanel::make('Default')
                            ->add('Home', url('/'))
                            ->add('Artikel', url('/artikel')),
                    ])
                    ->addMenuPanels([
                        StaticMenuPanel::make('halaman')
                            ->addMany(
                                \App\Models\Cms::where('jenis', 'halaman')
                                    ->pluck('slug', 'judul')
                                    ->map(fn($slug) => "/halaman/{$slug}")
                                    ->toArray()
                            )
                            ->description('Lorem ipsum...')
                            ->icon('heroicon-m-link')
                            ->collapsed(true)
                            ->collapsible(true)
                            ->paginate(perPage: 5, condition: true)
                    ])->usingResource(MenuWebsiteResource::class),
                BreezyCore::make()
                    ->myProfile(
                        shouldRegisterUserMenu: false, // Sets the 'account' link in the panel User Menu (default = true)
                        userMenuLabel: 'Profileku', // Customizes the 'account' link label in the panel User Menu (default = null)
                        shouldRegisterNavigation: false, // Adds a main navigation item for the My Profile page (default = false)
                        navigationGroup: 'Pengaturan', // Sets the navigation group for the My Profile page (default = null)
                        hasAvatars: false, // Enables the avatar upload form component (default = false)
                        slug: 'my-profile' // Sets the slug for the profile page (default = 'my-profile')
                    ),

                FilamentSpatieLaravelBackupPlugin::make()->usingPage(\App\Filament\Pages\Backup::class)->usingQueue('backupanku')->noTimeout(),
                \Croustibat\FilamentJobsMonitor\FilamentJobsMonitorPlugin::make(),



                \TomatoPHP\FilamentPlugins\FilamentPluginsPlugin::make()
                    ->discoverCurrentPanelOnly(),

                \Visualbuilder\EmailTemplates\EmailTemplatesPlugin::make(),

                FilamentShieldPlugin::make()
                    ->gridColumns([
                        'default' => 1,
                        'sm' => 2,
                        'lg' => 3
                    ])
                    ->sectionColumnSpan(1)
                    ->checkboxListColumns([
                        'default' => 1,
                        'sm' => 2,
                        'lg' => 4,
                    ])
                    ->resourceCheckboxListColumns([
                        'default' => 1,
                        'sm' => 2,
                    ]),

            ])->userMenuItems([
                'Nama' =>   MenuItem::make()
                    ->label(fn() => Auth::user()->name)
                    ->url(fn() => url('/admin/my-profile'))
                    ->sort(1)
                    ->icon('heroicon-o-user'),
                MenuItem::make('kasir')
                    ->label('Admin Kasir')
                    // ->icon('tabler-cash-register')
                    ->url(fn(): string => url('/adminkasir'))
                    ->openUrlInNewTab(true)
                    ->visible(fn() => $this->cekAplikasiAktif('Kasir'))
                    ->sort(2),
                MenuItem::make('hotel')
                    ->label('Admin Hotel')
                    // ->icon('tabler-building-skyscraper')
                    ->url(fn(): string => url('/adminhotel'))
                    ->openUrlInNewTab(true)
                    ->visible(fn() => $this->cekAplikasiAktif('Hotel'))
                    ->sort(3),

            ]);
    }



    /**
     * Memeriksa apakah aplikasi ada dan aktif
     *
     * @param string $namaAplikasi Nama folder aplikasi
     * @return bool
     */
    protected function cekAplikasiAktif(string $namaAplikasi): bool
    {
        $aplikasiPath = app_path("Aplikasi/{$namaAplikasi}");
        $configPath = $aplikasiPath . '/config.php';

        if (file_exists($configPath)) {
            $config = include $configPath;
            return isset($config['status']) && $config['status'] === 'aktif';
        }

        return false;
    }

    protected function webInfo()
    {
        $toko = Konfig::jcolObject('website');

        // Sekarang bisa menggunakan property object
        $brandName = $toko->judul ?? 'Admin Panel';
        $brandLogo = isset($toko->logo) ? url('storage/' . $toko->logo) : null;
        $favicon = isset($toko->favicon) ? url('storage/' . $toko->favicon) : null;


        return [
            'judul' => $brandName,
            'deskripsi' => $toko->deskripsi ?? 'Sistem manajemen hotel',
            'keywords' => $toko->keywords ?? 'hotel, manajemen, sistem',
            'favicon' => $favicon,
            'logo' => $brandLogo,
        ];
    }
}
