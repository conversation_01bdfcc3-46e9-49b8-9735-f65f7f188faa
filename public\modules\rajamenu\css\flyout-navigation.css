/* FilamentPHP Flyout Navigation CSS */

/* Top Navigation Flyout Menu Styles */
.fi-topbar-nav {
    position: relative;
}

/* Navigation Item dengan Children */
.fi-topbar-nav-item {
    position: relative;
}

/* Dropdown/Flyout Container */
.fi-topbar-nav-item .fi-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 250px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

/* Show dropdown on hover */
.fi-topbar-nav-item:hover .fi-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Dropdown Items */
.fi-dropdown-item {
    display: block;
    padding: 12px 16px;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 14px;
    font-weight: 500;
}

.fi-dropdown-item:hover {
    background-color: #f3f4f6;
    color: #1f2937;
}

/* Sub-level flyout (Level 2) */
.fi-dropdown-item.has-children {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.fi-dropdown-item.has-children::after {
    content: '›';
    font-size: 16px;
    color: #9ca3af;
}

/* Second level dropdown */
.fi-dropdown-item .fi-dropdown-sub {
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 200px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

/* Show sub-dropdown on hover */
.fi-dropdown-item:hover .fi-dropdown-sub {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

/* Dark mode support */
.dark .fi-dropdown,
.dark .fi-dropdown-sub {
    background: #1f2937;
    border-color: #374151;
}

.dark .fi-dropdown-item {
    color: #d1d5db;
}

.dark .fi-dropdown-item:hover {
    background-color: #374151;
    color: #f9fafb;
}

/* Navigation Group Styling */
.fi-topbar-nav-group {
    position: relative;
    display: inline-block;
}

.fi-topbar-nav-group-label {
    padding: 8px 16px;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.fi-topbar-nav-group-label:hover {
    color: #f59e0b;
}

.fi-topbar-nav-group-label::after {
    content: '▼';
    font-size: 10px;
    transition: transform 0.2s ease;
}

.fi-topbar-nav-group:hover .fi-topbar-nav-group-label::after {
    transform: rotate(180deg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .fi-dropdown,
    .fi-dropdown-sub {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        border-radius: 0;
        padding: 0;
        background: transparent;
    }
    
    .fi-dropdown-item {
        padding-left: 32px;
    }
    
    .fi-dropdown-sub .fi-dropdown-item {
        padding-left: 48px;
    }
}

/* Animation untuk smooth transitions */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideRight {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fi-dropdown {
    animation: slideDown 0.3s ease;
}

.fi-dropdown-sub {
    animation: slideRight 0.3s ease;
}

/* Custom styling untuk FilamentPHP theme */
.fi-topbar .fi-topbar-nav-item .fi-btn {
    position: relative;
}

/* Indicator untuk menu yang memiliki children */
.fi-topbar-nav-item.has-dropdown > .fi-btn::after {
    content: '▼';
    margin-left: 4px;
    font-size: 10px;
    transition: transform 0.2s ease;
}

.fi-topbar-nav-item.has-dropdown:hover > .fi-btn::after {
    transform: rotate(180deg);
}

/* Styling untuk icon di dropdown */
.fi-dropdown-item .fi-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

/* Separator dalam dropdown */
.fi-dropdown-separator {
    height: 1px;
    background-color: #e5e7eb;
    margin: 4px 0;
}

.dark .fi-dropdown-separator {
    background-color: #374151;
}

/* Simple Flyout Navigation Styles */
.simple-flyout-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.nav-group {
    position: relative;
    display: inline-block;
}

.nav-group-label {
    padding: 0.5rem 1rem;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.nav-group-label:hover {
    background-color: #f3f4f6;
    color: #f59e0b;
}

.nav-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.nav-group:hover .nav-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.nav-item {
    display: block;
    padding: 12px 16px;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
}

.nav-item:hover {
    background-color: #f3f4f6;
    color: #1f2937;
}

.nav-item.has-children::after {
    content: '›';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #9ca3af;
}

.nav-sub-dropdown {
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 180px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.nav-item:hover .nav-sub-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.nav-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    display: inline-block;
}
