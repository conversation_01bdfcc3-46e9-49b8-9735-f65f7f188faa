<?php

namespace App\Services;

use Filament\Navigation\NavigationItem;
use Illuminate\Support\Collection;

class FlyoutNavigationService
{
    /**
     * Organize navigation items into parent-child structure for flyout menu
     */
    public static function organizeNavigationItems(array $navigationItems): array
    {
        $organized = [
            'groups' => [],
            'childItems' => []
        ];

        $groupedItems = collect($navigationItems)->groupBy(function ($item) {
            return $item->getGroup() ?? 'Default';
        });

        foreach ($groupedItems as $groupName => $items) {
            $parentItems = [];
            $childItems = [];

            foreach ($items as $item) {
                $parentItem = $item->getParentItem();
                
                if ($parentItem) {
                    // This is a child item
                    if (!isset($childItems[$parentItem])) {
                        $childItems[$parentItem] = [];
                    }
                    $childItems[$parentItem][] = $item;
                } else {
                    // This is a parent item
                    $parentItems[] = $item;
                }
            }

            $organized['groups'][$groupName] = $parentItems;
            $organized['childItems'] = array_merge($organized['childItems'], $childItems);
        }

        return $organized;
    }

    /**
     * Create navigation items for CMS group with flyout structure
     */
    public static function createCmsNavigationItems(): array
    {
        return [
            // Parent: Content Management
            NavigationItem::make('Content')
                ->icon('heroicon-o-book-open')
                ->url('#')
                ->group('Cms')
                ->sort(1),

            // Child items untuk Content
            NavigationItem::make('Konten')
                ->icon('heroicon-o-document-text')
                ->url(fn(): string => \App\Filament\Resources\CmsResource::getUrl('index'))
                ->group('Cms')
                ->parentItem('Content')
                ->sort(1),

            NavigationItem::make('Artikel')
                ->icon('heroicon-o-newspaper')
                ->url(fn(): string => \App\Filament\Pages\Cms\Kategori::getUrl())
                ->group('Cms')
                ->parentItem('Content')
                ->sort(2),

            NavigationItem::make('Media Library')
                ->icon('heroicon-o-photo')
                ->url('#')
                ->group('Cms')
                ->parentItem('Content')
                ->sort(3),

            NavigationItem::make('Kategori artikel')
                ->icon('heroicon-o-tag')
                ->url(fn(): string => \App\Filament\Pages\Cms\Kategori::getUrl())
                ->group('Cms')
                ->parentItem('Content')
                ->sort(4),

            NavigationItem::make('Menu')
                ->icon('heroicon-o-queue-list')
                ->url(fn(): string => \App\Filament\Resources\MenuWebsiteResource::getUrl('index'))
                ->group('Cms')
                ->parentItem('Content')
                ->sort(5),

            NavigationItem::make('Tema')
                ->icon('heroicon-o-paint-brush')
                ->url(fn(): string => \App\Filament\Resources\CmsResource::getUrl('tema'))
                ->group('Cms')
                ->parentItem('Content')
                ->sort(6),

            // Parent: Design
            NavigationItem::make('Design')
                ->icon('heroicon-o-paint-brush')
                ->url('#')
                ->group('Cms')
                ->sort(10),

            NavigationItem::make('Templates')
                ->icon('heroicon-o-document-duplicate')
                ->url('#')
                ->group('Cms')
                ->parentItem('Design')
                ->sort(1),

            NavigationItem::make('Widgets')
                ->icon('heroicon-o-squares-2x2')
                ->url('#')
                ->group('Cms')
                ->parentItem('Design')
                ->sort(2),
        ];
    }

    /**
     * Create navigation items for System group with flyout structure
     */
    public static function createSystemNavigationItems(): array
    {
        return [
            // Parent: Data Management
            NavigationItem::make('Data Management')
                ->icon('heroicon-o-database')
                ->url('#')
                ->group('System')
                ->sort(1),

            NavigationItem::make('Seluruh kategori')
                ->icon('heroicon-o-list-bullet')
                ->url(fn(): string => \App\Filament\Resources\KategoriResource::getUrl('index'))
                ->group('System')
                ->parentItem('Data Management')
                ->sort(1),

            NavigationItem::make('Metode pembayaran')
                ->icon('heroicon-o-credit-card')
                ->url(fn(): string => \App\Filament\Resources\MetodePembayaranUtamaResource::getUrl('index'))
                ->group('System')
                ->parentItem('Data Management')
                ->sort(2),

            NavigationItem::make('Data')
                ->icon('heroicon-o-table-cells')
                ->url(fn(): string => \App\Filament\Pages\Data::getUrl())
                ->group('System')
                ->parentItem('Data Management')
                ->sort(3),

            // Parent: System Tools
            NavigationItem::make('System Tools')
                ->icon('heroicon-o-wrench-screwdriver')
                ->url('#')
                ->group('System')
                ->sort(10),

            NavigationItem::make('Rute')
                ->icon('heroicon-o-map')
                ->url(fn(): string => \App\Filament\Pages\RouteList::getUrl())
                ->group('System')
                ->parentItem('System Tools')
                ->sort(1),

            NavigationItem::make('Aplikasi')
                ->icon('heroicon-o-squares-plus')
                ->url(fn(): string => \App\Filament\Pages\AplikasiRajaDesain::getUrl())
                ->group('System')
                ->parentItem('System Tools')
                ->sort(2),

            NavigationItem::make('Konfig')
                ->icon('heroicon-o-cog-6-tooth')
                ->url(fn(): string => \App\Filament\Resources\KonfigResource::getUrl('index'))
                ->group('System')
                ->parentItem('System Tools')
                ->sort(3),
        ];
    }

    /**
     * Create navigation items for Settings group with flyout structure
     */
    public static function createSettingsNavigationItems(): array
    {
        return [
            // Parent: Business Management
            NavigationItem::make('Business')
                ->icon('heroicon-o-building-office')
                ->url('#')
                ->group('Pengaturan')
                ->sort(1),

            NavigationItem::make('Usaha')
                ->icon('heroicon-o-building-storefront')
                ->url(fn(): string => \App\Filament\Resources\TokoResource::getUrl('index'))
                ->group('Pengaturan')
                ->parentItem('Business')
                ->sort(1),

            NavigationItem::make('Staff')
                ->icon('heroicon-o-users')
                ->url(fn(): string => \App\Filament\Resources\UserResource::getUrl('index'))
                ->group('Pengaturan')
                ->parentItem('Business')
                ->sort(2),
        ];
    }

    /**
     * Get all organized navigation items
     */
    public static function getAllNavigationItems(): array
    {
        return array_merge(
            self::createCmsNavigationItems(),
            self::createSystemNavigationItems(),
            self::createSettingsNavigationItems()
        );
    }
}
