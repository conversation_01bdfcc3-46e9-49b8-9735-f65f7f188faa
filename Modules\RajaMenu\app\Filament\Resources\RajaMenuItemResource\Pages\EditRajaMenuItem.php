<?php

namespace Modules\RajaMenu\Filament\Resources\RajaMenuItemResource\Pages;

use Modules\RajaMenu\Filament\Resources\RajaMenuItemResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditRajaMenuItem extends EditRecord
{
    protected static string $resource = RajaMenuItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->visible(fn () => !$this->record->is_auto_discovered),
        ];
    }
}
